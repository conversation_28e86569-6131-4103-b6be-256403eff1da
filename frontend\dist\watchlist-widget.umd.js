!function(){"use strict";try{if("undefined"!=typeof document){var t=document.createElement("style");t.appendChild(document.createTextNode(".tab-bar{border-bottom:1px solid #e1e3e6;padding:0;overflow-x:auto;overflow-y:hidden}.tabs-container{display:flex;align-items:flex-end;gap:2px;min-width:min-content}.tab{display:flex;align-items:center;min-width:120px;max-width:250px;height:30px;background:#fff;border:1px solid #e1e3e6;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;position:relative;padding:0 8px;margin-bottom:1px}.tab:hover{background:#dee2e6}.tab.active{background:#e9ecef;border-color:#d1d4dc;margin-bottom:0;z-index:1}.tab-content{display:flex;align-items:center;gap:4px;flex:1;min-width:0}.tab-name{font-size:14px;font-weight:500;color:#131722;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1}.tab-star{color:#ffc107;flex-shrink:0}.tab-count{font-size:12px;color:#6c757d;flex-shrink:0}.tab-actions{display:flex;align-items:center;gap:2px;opacity:0;transition:opacity .2s;margin-left:16px}.tab:hover .tab-actions,.tab.active .tab-actions{opacity:1}.tab-action-btn{display:flex;align-items:center;justify-content:center;border:none;border-radius:3px;background:transparent;color:#6c757d;cursor:pointer;transition:all .2s}button.tab-action-btn{width:20px;height:20px;padding:0}.tab-action-btn:hover{background:#0000001a;color:#131722}.close-tab-btn:hover{background:#f443361a;color:#f44336}.tab-action-btn:disabled{opacity:.3;cursor:not-allowed}.tab-action-btn:disabled:hover{background:transparent;color:#6c757d}.tab-edit-form{display:flex;align-items:center;gap:4px;flex:1;padding:2px}.tab-edit-input{flex:1;padding:4px 6px;border:1px solid #2962ff;border-radius:3px;background:#fff;color:#131722;font-size:14px;font-weight:500;min-width:80px}.tab-edit-input:focus{outline:none;border-color:#2962ff}.tab-edit-actions{display:flex;align-items:center;gap:2px;flex-shrink:0}.confirm-edit-btn{display:flex;align-items:center;justify-content:center;border:1px solid #00c853;border-radius:3px;background:transparent;color:#00c853;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.confirm-edit-btn{width:20px;height:20px;padding:0}.confirm-edit-btn:hover{background:#00c8531a}.cancel-edit-btn{display:flex;align-items:center;justify-content:center;border:1px solid #6c757d;border-radius:3px;background:transparent;color:#6c757d;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.cancel-edit-btn{width:20px;height:20px;padding:0}.cancel-edit-btn:hover{background:#6c757d1a}.delete-confirm{display:flex;align-items:center;gap:2px}.confirm-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #f44336;color:#f44336;cursor:pointer;transition:background-color .2s}button.confirm-delete-btn{width:20px;height:20px;padding:0}.cancel-delete-btn{display:flex;align-items:center;justify-content:center;border-radius:3px;border:1px solid #6c757d;color:#6c757d;cursor:pointer;transition:background-color .2s}button.cancel-delete-btn{width:20px;height:20px;padding:0}.add-tab-btn{display:flex;align-items:center;justify-content:center;background:#fff;border:1px solid #e1e3e6;border-bottom:none;border-radius:4px 4px 0 0;cursor:pointer;transition:all .2s;color:#6c757d;margin-bottom:1px;flex-shrink:0}button.add-tab-btn{width:30px;height:30px;padding:0}.add-tab-btn:hover{background:#dee2e6;color:#131722}.add-tab-form{display:flex;align-items:center;gap:4px;min-width:200px;height:30px;background:#fff;border:1px solid #2962ff;border-bottom:none;border-radius:4px 4px 0 0;padding:0 8px;margin-bottom:1px;flex-shrink:0}.add-tab-input{flex:1;padding:4px 6px;border:none;background:transparent;color:#131722;font-size:14px;font-weight:500;min-width:100px}.add-tab-input:focus{outline:none}.add-tab-input::placeholder{color:#6c757d}.add-tab-actions{display:flex;align-items:center;gap:2px;flex-shrink:0}.confirm-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #00c853;border-radius:3px;color:#00c853;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.confirm-add-btn{width:20px;height:20px;padding:0}.cancel-add-btn{display:flex;align-items:center;justify-content:center;border:1px solid #6c757d;border-radius:3px;color:#6c757d;cursor:pointer;transition:background-color .2s;flex-shrink:0}button.cancel-add-btn{width:20px;height:20px;padding:0}@media (max-width: 768px){.tab{min-width:120px;max-width:180px}}@media (max-width: 480px){.tab{min-width:120px;max-width:120px;padding:0 4px}.tab-name{font-size:12px}.tab-count{display:none}}.add-instrument-section{position:relative;width:100%;padding:8px 0;border-top:1px solid #e1e3e6}.add-section-title{margin:0 0 8px;font-size:16px;font-weight:600;color:#131722}.search-container{position:relative}.search-icon{position:absolute;left:10px;top:50%;transform:translateY(-50%);color:#6c757d}.search-input{width:100%;padding:10px 8px 10px 32px;border:1px solid #d1d4dc;border-radius:6px;background:#fff;color:#131722;font-size:14px}.search-input:focus{outline:none;border-color:#2962ff}.search-results{position:absolute;top:84px;left:0;right:0;border:1px solid #e1e3e6;border-radius:4%;background:#fff;margin-bottom:16px;z-index:999;box-shadow:0 2px 4px #0000001a}.search-results-empty{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:24px;color:var(--text-secondary);text-align:center;min-height:120px}.search-results-empty-icon{font-size:24px;margin-bottom:8px;color:#d1d4dc}.search-results-empty-text{font-size:14px;color:#6c757d}.search-results .rc-virtual-list,.search-results .rc-virtual-list-holder,.search-results .rc-virtual-list-holder-inner{border-radius:6px}.search-result-item{display:flex;align-items:center;justify-content:space-between;cursor:pointer;padding:12px 16px;gap:4px;border-bottom:1px solid #f1f3f4;transition:background-color .2s;height:60px;box-sizing:border-box}.search-result-item:hover{background:#f8f9fa}.search-result-item:last-child{border-bottom:none}.instrument-info{display:flex;align-items:center;justify-content:space-between;gap:16px;flex:1}.instrument-info .symbol{font-weight:600;color:#131722;min-width:60px}.symbol-info{display:flex;align-items:left;flex-direction:column}.instrument-info .name{font-size:12px;color:#6c757d;flex:1}.instrument-info .abbreviation{font-weight:600;color:#131722;min-width:80px}.instrument-info .change{font-weight:500;min-width:100px}.instrument-info .change.positive{color:#00c853}.instrument-info .change.negative{color:#f44336}.add-instrument-btn{display:flex;align-items:center;justify-content:center;color:#a4a6ac;border:none;border-radius:4px;padding-left:16px;cursor:pointer;transition:background-color .2s}button.add-instrument-btn{width:36px;height:36px;padding:0}button.add-instrument-btn:hover{background:#d8dbe2;color:#fff}button.add-instrument-btn:disabled{background:#d1d4dc;cursor:not-allowed}@media (max-width: 768px){.instrument-info{flex-direction:column;align-items:flex-start;gap:4px}.instrument-info .symbol,.instrument-info .price,.instrument-info .change{min-width:auto}}.instruments-section{flex:1;padding:10px 0 0;display:flex;flex-direction:column}.instruments-table-container{width:100%;border:1px solid #e1e3e6;border-radius:6px;overflow-x:auto}.instruments-table{width:100%;border-collapse:collapse;background:#fff;table-layout:fixed;min-width:600px}.instruments-table thead{position:sticky;top:0;z-index:1}.table-header{background:#f8f9fa;padding:8px 12px;font-weight:600;font-size:12px;text-transform:uppercase;letter-spacing:.5px;color:#6c757d;border-bottom:1px solid #e1e3e6;text-align:left;white-space:nowrap}.instruments-table th:nth-child(1){width:35%;min-width:120px}.instruments-table th:nth-child(2){width:25%;min-width:100px}.instruments-table th:nth-child(3){width:20%;min-width:80px}.instruments-table th:nth-child(4){width:20%;min-width:100px}.table-row{display:table-row;position:relative;cursor:pointer;border-bottom:1px solid #f1f3f4;transition:background-color .2s}.table-row td{display:table-cell;padding:8px 12px;vertical-align:middle;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.header-cell{display:flex;align-items:center}.symbol{flex-direction:column;align-items:flex-start}.symbol-text{font-weight:600}.market-text{font-size:11px;color:#6c757d}.positive{color:#28a745}.negative{color:#dc3545}.remove-btn{background:none;border:none;cursor:pointer;color:#dc3545;padding:0}.table-row:hover .remove-btn{visibility:visible}.table-row.selected{background:#e8f0fe}.table-row:last-child{border-bottom:none}.cell{font-size:14px;font-weight:500}.cell.symbol{display:flex;flex-direction:column;align-items:flex-start;gap:2px}.symbol-text{font-weight:600;color:#131722}.market-text{font-size:11px;color:#6c757d;font-weight:400}.cell.name{color:#6c757d;font-weight:400}.cell.price{color:#131722;font-weight:600}.cell.change{font-weight:500}.cell.change-percent{text-align:end;font-weight:500;position:relative;display:flex;align-items:center;justify-content:flex-end;gap:8px}.change-value{flex:1;text-align:right}.cell.change.positive{color:#26a69a}.cell.change.negative{color:#f44336}.cell.change-percent.positive{color:#26a69a}.cell.change-percent.negative{color:#f44336}.cell.high,.cell.low,.cell.week-high{color:#131722;font-weight:600}.remove-btn{background:none;visibility:hidden;position:static;background:#fff;border:none;cursor:pointer;padding:6px;border-radius:4px;color:#6c757d;transition:all .2s;flex-shrink:0}.remove-btn:hover{background:#f3938c;color:#f44336}.delete-confirm{display:flex;gap:4px}.confirm-delete-btn,.cancel-delete-btn{background:none;border:none;cursor:pointer;padding:6px;border-radius:4px;display:flex;align-items:center;justify-content:center;transition:all .2s}.confirm-delete-btn{color:#f44336}.confirm-delete-btn:hover{background:#f443361a}.cancel-delete-btn{color:#6c757d}.cancel-delete-btn:hover{background:#6c757d1a}@keyframes shimmer{0%{background-position:-200px 0}to{background-position:calc(200px + 100%) 0}}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.skeleton-row{border-bottom:1px solid #f1f3f4;animation:fadeIn .3s ease-out}.skeleton-row:hover{background:transparent}.skeleton-cell{padding:12px;vertical-align:middle}.skeleton-line{background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200px 100%;animation:shimmer 1.5s infinite;border-radius:4px;height:16px}.skeleton-symbol{display:flex;flex-direction:column;gap:6px}.skeleton-symbol-text{width:80px;height:18px}.skeleton-market-text{width:60px;height:12px}.skeleton-price{width:70px}.skeleton-change{width:50px}.skeleton-change-percent{width:60px;margin-left:auto}.loading-state,.empty-state,.error-state{display:flex;flex-direction:column;justify-content:center;align-items:center;padding:60px 20px;text-align:center;animation:fadeIn .5s ease-out}.loading-state{background:linear-gradient(135deg,#f8f9fa,#fff);border-radius:12px;margin:20px;border:1px solid #e9ecef}.empty-state{border-radius:12px;margin:20px}.error-state{background:linear-gradient(135deg,#fff5f5,#fff);border-radius:12px;margin:20px;border:1px solid #fed7d7}.loading-icon,.empty-icon,.error-icon{font-size:48px;margin-bottom:16px;animation:pulse 2s infinite}.loading-icon{animation:pulse 1.5s infinite}.empty-icon{opacity:.7}.error-icon{color:#f44336}.loading-title,.empty-title,.error-title{font-size:18px;font-weight:600;color:#2d3748;margin:0 0 8px;line-height:1.4}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:14px;color:#718096;margin:0;line-height:1.5;max-width:300px}.error-title{color:#e53e3e}.error-subtitle{color:#c53030}.empty-state p,.loading-state p,.error-state p{margin:4px 0;font-size:14px}.error-state{color:#f44336}@media (max-width: 768px){.instruments-table{min-width:500px}.table-header{padding:6px 8px;font-size:11px}.table-row td{padding:6px 8px;font-size:13px}.cell.symbol{gap:1px}.symbol-text{font-size:13px}.market-text{font-size:10px}.instruments-table th:nth-child(1){width:40%;min-width:100px}.instruments-table th:nth-child(2){width:25%;min-width:80px}.instruments-table th:nth-child(3){width:18%;min-width:70px}.instruments-table th:nth-child(4){width:17%;min-width:80px}.change-value{font-size:12px}.remove-btn{padding:4px}}@media (max-width: 768px){.loading-state,.empty-state,.error-state{padding:40px 16px;margin:16px}.loading-icon,.empty-icon,.error-icon{font-size:36px;margin-bottom:12px}.loading-title,.empty-title,.error-title{font-size:16px}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:13px}}@media (max-width: 480px){.instruments-table{min-width:400px}.table-header{padding:4px 6px;font-size:10px}.table-row td{padding:4px 6px;font-size:12px}.symbol-text{font-size:12px}.market-text{font-size:9px}.instruments-table th:nth-child(1){width:45%;min-width:90px}.instruments-table th:nth-child(2){width:25%;min-width:70px}.instruments-table th:nth-child(3){width:15%;min-width:60px}.instruments-table th:nth-child(4){width:15%;min-width:70px}.remove-btn{padding:3px}.change-value{font-size:11px}.cell.change-percent{gap:4px}.skeleton-symbol-text{width:60px}.skeleton-market-text{width:45px}.skeleton-price{width:50px}.skeleton-change{width:40px}.skeleton-change-percent{width:45px}.loading-state,.empty-state,.error-state{padding:30px 12px;margin:12px}.loading-icon,.empty-icon,.error-icon{font-size:32px;margin-bottom:10px}.loading-title,.empty-title,.error-title{font-size:15px}.loading-subtitle,.empty-subtitle,.error-subtitle{font-size:12px}}@media (max-width: 360px){.instruments-table{min-width:320px}.table-header{padding:3px 4px;font-size:9px}.table-row td{padding:3px 4px;font-size:11px}.symbol-text{font-size:11px}.market-text{font-size:8px}.instruments-table th:nth-child(1){width:50%;min-width:80px}.instruments-table th:nth-child(2){width:25%;min-width:60px}.instruments-table th:nth-child(3){width:12.5%;min-width:50px}.instruments-table th:nth-child(4){width:12.5%;min-width:60px}.remove-btn{padding:2px}.change-value{font-size:10px}.cell.change-percent{gap:2px}}")),document.head.appendChild(t)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}}();
!function(e){"function"==typeof define&&define.amd?define(e):e()}(function(){"use strict";var e,t,n,r,i,o,s,a,c,l,u,d,h,f={},p=[],m=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,v=Array.isArray;function y(e,t){for(var n in t)e[n]=t[n];return e}function g(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function b(t,n,r){var i,o,s,a={};for(s in n)"key"==s?i=n[s]:"ref"==s?o=n[s]:a[s]=n[s];if(arguments.length>2&&(a.children=arguments.length>3?e.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(s in t.defaultProps)void 0===a[s]&&(a[s]=t.defaultProps[s]);return _(t,a,i,o,null)}function _(e,r,i,o,s){var a={type:e,props:r,key:i,ref:o,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==s?++n:s,__i:-1,__u:0};return null==s&&null!=t.vnode&&t.vnode(a),a}function w(e){return e.children}function x(e,t){this.props=e,this.context=t}function k(e,t){if(null==t)return e.__?k(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?k(e):null}function S(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return S(e)}}function E(e){(!e.__d&&(e.__d=!0)&&i.push(e)&&!O.__r++||o!=t.debounceRendering)&&((o=t.debounceRendering)||s)(O)}function O(){for(var e,n,r,o,s,c,l,u=1;i.length;)i.length>u&&i.sort(a),e=i.shift(),u=i.length,e.__d&&(r=void 0,s=(o=(n=e).__v).__e,c=[],l=[],n.__P&&((r=y({},o)).__v=o.__v+1,t.vnode&&t.vnode(r),j(n.__P,r,o,n.__n,n.__P.namespaceURI,32&o.__u?[s]:null,c,null==s?k(o):s,!!(32&o.__u),l),r.__v=o.__v,r.__.__k[r.__i]=r,F(c,r,l),r.__e!=s&&S(r)));O.__r=0}function C(e,t,n,r,i,o,s,a,c,l,u){var d,h,m,y,g,b,x=r&&r.__k||p,S=t.length;for(c=function(e,t,n,r,i){var o,s,a,c,l,u=n.length,d=u,h=0;for(e.__k=new Array(i),o=0;o<i;o++)null!=(s=t[o])&&"boolean"!=typeof s&&"function"!=typeof s?(c=o+h,(s=e.__k[o]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?_(null,s,null,null,null):v(s)?_(w,{children:s},null,null,null):null==s.constructor&&s.__b>0?_(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=e,s.__b=e.__b+1,a=null,-1!=(l=s.__i=N(s,n,c,d))&&(d--,(a=n[l])&&(a.__u|=2)),null==a||null==a.__v?(-1==l&&(i>u?h--:i<u&&h++),"function"!=typeof s.type&&(s.__u|=4)):l!=c&&(l==c-1?h--:l==c+1?h++:(l>c?h--:h++,s.__u|=4))):e.__k[o]=null;if(d)for(o=0;o<u;o++)null!=(a=n[o])&&!(2&a.__u)&&(a.__e==r&&(r=k(a)),I(a,a));return r}(n,t,x,c,S),d=0;d<S;d++)null!=(m=n.__k[d])&&(h=-1==m.__i?f:x[m.__i]||f,m.__i=d,b=j(e,m,h,i,o,s,a,c,l,u),y=m.__e,m.ref&&h.ref!=m.ref&&(h.ref&&L(h.ref,null,m),u.push(m.ref,m.__c||y,m)),null==g&&null!=y&&(g=y),4&m.__u||h.__k===m.__k?c=R(m,c,e):"function"==typeof m.type&&void 0!==b?c=b:y&&(c=y.nextSibling),m.__u&=-7);return n.__e=g,c}function R(e,t,n){var r,i;if("function"==typeof e.type){for(r=e.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=e,t=R(r[i],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=k(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function A(e,t){return t=t||[],null==e||"boolean"==typeof e||(v(e)?e.some(function(e){A(e,t)}):t.push(e)),t}function N(e,t,n,r){var i,o,s=e.key,a=e.type,c=t[n];if(null===c&&null==e.key||c&&s==c.key&&a==c.type&&!(2&c.__u))return n;if(r>(null==c||2&c.__u?0:1))for(i=n-1,o=n+1;i>=0||o<t.length;){if(i>=0){if((c=t[i])&&!(2&c.__u)&&s==c.key&&a==c.type)return i;i--}if(o<t.length){if((c=t[o])&&!(2&c.__u)&&s==c.key&&a==c.type)return o;o++}}return-1}function P(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||m.test(t)?n:n+"px"}function T(e,t,n,r,i){var o,s;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||P(e.style,t,"");if(n)for(t in n)r&&n[t]==r[t]||P(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])o=t!=(t=t.replace(c,"$1")),s=t.toLowerCase(),t=s in e||"onFocusOut"==t||"onFocusIn"==t?s.slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r?n.u=r.u:(n.u=l,e.addEventListener(t,o?d:u,o)):e.removeEventListener(t,o?d:u,o);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(a){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function M(e){return function(n){if(this.l){var r=this.l[n.type+e];if(null==n.t)n.t=l++;else if(n.t<r.u)return;return r(t.event?t.event(n):n)}}}function j(n,r,i,o,s,a,c,l,u,d){var h,p,m,b,_,S,E,O,R,A,N,P,M,j,F,L,I,U=r.type;if(null!=r.constructor)return null;128&i.__u&&(u=!!(32&i.__u),a=[l=r.__e=i.__e]),(h=t.__b)&&h(r);e:if("function"==typeof U)try{if(O=r.props,R="prototype"in U&&U.prototype.render,A=(h=U.contextType)&&o[h.__c],N=h?A?A.props.value:h.__:o,i.__c?E=(p=r.__c=i.__c).__=p.__E:(R?r.__c=p=new U(O,N):(r.__c=p=new x(O,N),p.constructor=U,p.render=q),A&&A.sub(p),p.props=O,p.state||(p.state={}),p.context=N,p.__n=o,m=p.__d=!0,p.__h=[],p._sb=[]),R&&null==p.__s&&(p.__s=p.state),R&&null!=U.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=y({},p.__s)),y(p.__s,U.getDerivedStateFromProps(O,p.__s))),b=p.props,_=p.state,p.__v=r,m)R&&null==U.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),R&&null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(R&&null==U.getDerivedStateFromProps&&O!==b&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(O,N),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(O,p.__s,N)||r.__v==i.__v){for(r.__v!=i.__v&&(p.props=O,p.state=p.__s,p.__d=!1),r.__e=i.__e,r.__k=i.__k,r.__k.some(function(e){e&&(e.__=r)}),P=0;P<p._sb.length;P++)p.__h.push(p._sb[P]);p._sb=[],p.__h.length&&c.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(O,p.__s,N),R&&null!=p.componentDidUpdate&&p.__h.push(function(){p.componentDidUpdate(b,_,S)})}if(p.context=N,p.props=O,p.__P=n,p.__e=!1,M=t.__r,j=0,R){for(p.state=p.__s,p.__d=!1,M&&M(r),h=p.render(p.props,p.state,p.context),F=0;F<p._sb.length;F++)p.__h.push(p._sb[F]);p._sb=[]}else do{p.__d=!1,M&&M(r),h=p.render(p.props,p.state,p.context),p.state=p.__s}while(p.__d&&++j<25);p.state=p.__s,null!=p.getChildContext&&(o=y(y({},o),p.getChildContext())),R&&!m&&null!=p.getSnapshotBeforeUpdate&&(S=p.getSnapshotBeforeUpdate(b,_)),L=h,null!=h&&h.type===w&&null==h.key&&(L=D(h.props.children)),l=C(n,v(L)?L:[L],r,i,o,s,a,c,l,u,d),p.base=r.__e,r.__u&=-161,p.__h.length&&c.push(p),E&&(p.__E=p.__=null)}catch(z){if(r.__v=null,u||null!=a)if(z.then){for(r.__u|=u?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;a[a.indexOf(l)]=null,r.__e=l}else for(I=a.length;I--;)g(a[I]);else r.__e=i.__e,r.__k=i.__k;t.__e(z,r,i)}else null==a&&r.__v==i.__v?(r.__k=i.__k,r.__e=i.__e):l=r.__e=function(n,r,i,o,s,a,c,l,u){var d,h,p,m,y,b,_,w=i.props,x=r.props,S=r.type;if("svg"==S?s="http://www.w3.org/2000/svg":"math"==S?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=a)for(d=0;d<a.length;d++)if((y=a[d])&&"setAttribute"in y==!!S&&(S?y.localName==S:3==y.nodeType)){n=y,a[d]=null;break}if(null==n){if(null==S)return document.createTextNode(x);n=document.createElementNS(s,S,x.is&&x),l&&(t.__m&&t.__m(r,a),l=!1),a=null}if(null==S)w===x||l&&n.data==x||(n.data=x);else{if(a=a&&e.call(n.childNodes),w=i.props||f,!l&&null!=a)for(w={},d=0;d<n.attributes.length;d++)w[(y=n.attributes[d]).name]=y.value;for(d in w)if(y=w[d],"children"==d);else if("dangerouslySetInnerHTML"==d)p=y;else if(!(d in x)){if("value"==d&&"defaultValue"in x||"checked"==d&&"defaultChecked"in x)continue;T(n,d,null,y,s)}for(d in x)y=x[d],"children"==d?m=y:"dangerouslySetInnerHTML"==d?h=y:"value"==d?b=y:"checked"==d?_=y:l&&"function"!=typeof y||w[d]===y||T(n,d,y,w[d],s);if(h)l||p&&(h.__html==p.__html||h.__html==n.innerHTML)||(n.innerHTML=h.__html),r.__k=[];else if(p&&(n.innerHTML=""),C("template"==r.type?n.content:n,v(m)?m:[m],r,i,o,"foreignObject"==S?"http://www.w3.org/1999/xhtml":s,a,c,a?a[0]:i.__k&&k(i,0),l,u),null!=a)for(d=a.length;d--;)g(a[d]);l||(d="value","progress"==S&&null==b?n.removeAttribute("value"):null!=b&&(b!==n[d]||"progress"==S&&!b||"option"==S&&b!=w[d])&&T(n,d,b,w[d],s),d="checked",null!=_&&_!=n[d]&&T(n,d,_,w[d],s))}return n}(i.__e,r,i,o,s,a,c,u,d);return(h=t.diffed)&&h(r),128&r.__u?void 0:l}function F(e,n,r){for(var i=0;i<r.length;i++)L(r[i],r[++i],r[++i]);t.__c&&t.__c(n,e),e.some(function(n){try{e=n.__h,n.__h=[],e.some(function(e){e.call(n)})}catch(r){t.__e(r,n.__v)}})}function D(e){return"object"!=typeof e||null==e||e.__b&&e.__b>0?e:v(e)?e.map(D):y({},e)}function L(e,n,r){try{if("function"==typeof e){var i="function"==typeof e.__u;i&&e.__u(),i&&null==n||(e.__u=e(n))}else e.current=n}catch(o){t.__e(o,r)}}function I(e,n,r){var i,o;if(t.unmount&&t.unmount(e),(i=e.ref)&&(i.current&&i.current!=e.__e||L(i,null,n)),null!=(i=e.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(s){t.__e(s,n)}i.base=i.__P=null}if(i=e.__k)for(o=0;o<i.length;o++)i[o]&&I(i[o],n,r||"function"!=typeof e.type);r||g(e.__e),e.__c=e.__=e.__e=void 0}function q(e,t,n){return this.constructor(e,n)}function U(n,r,i){var o,s,a,c;r==document&&(r=document.documentElement),t.__&&t.__(n,r),s=(o="function"==typeof i)?null:i&&i.__k||r.__k,a=[],c=[],j(r,n=(!o&&i||r).__k=b(w,null,[n]),s||f,f,r.namespaceURI,!o&&i?[i]:s?null:r.firstChild?e.call(r.childNodes):null,a,!o&&i?i:s?s.__e:r.firstChild,o,c),F(a,n,c)}function z(e,t){U(e,t,z)}function B(t,n,r){var i,o,s,a,c=y({},t.props);for(s in t.type&&t.type.defaultProps&&(a=t.type.defaultProps),n)"key"==s?i=n[s]:"ref"==s?o=n[s]:c[s]=void 0===n[s]&&null!=a?a[s]:n[s];return arguments.length>2&&(c.children=arguments.length>3?e.call(arguments,2):r),_(t.type,c,i||t.key,o||t.ref,null)}function H(e){function t(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t.__c]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!=e.value&&n.forEach(function(e){e.__e=!0,E(e)})},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}return t.__c="__cC"+h++,t.__=e,t.Provider=t.__l=(t.Consumer=function(e,t){return e.children(t)}).contextType=t,t}e=p.slice,t={__e:function(e,t,n,r){for(var i,o,s;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),s=i.__d),s)return i.__E=i}catch(a){e=a}throw e}},n=0,r=function(e){return null!=e&&null==e.constructor},x.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=y({},this.state),"function"==typeof e&&(e=e(y({},n),this.props)),e&&y(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),E(this))},x.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),E(this))},x.prototype.render=w,i=[],s="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,a=function(e,t){return e.__v.__b-t.__v.__b},O.__r=0,c=/(PointerCapture)$|Capture$/i,l=0,u=M(!1),d=M(!0),h=0;var W=0;function $(e,n,r,i,o,s){n||(n={});var a,c,l=n;if("ref"in l)for(c in l={},n)"ref"==c?a=n[c]:l[c]=n[c];var u={type:e,props:l,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--W,__i:-1,__u:0,__source:o,__self:s};if("function"==typeof e&&(a=e.defaultProps))for(c in a)void 0===l[c]&&(l[c]=a[c]);return t.vnode&&t.vnode(u),u}var Q,K,V="Document",G="OperationDefinition",J="FragmentDefinition";class Y extends Error{constructor(e,t,n,r,i,o,s){super(e),this.name="GraphQLError",this.message=e,i&&(this.path=i),t&&(this.nodes=Array.isArray(t)?t:[t]),n&&(this.source=n),r&&(this.positions=r),o&&(this.originalError=o);var a=s;if(!a&&o){var c=o.extensions;c&&"object"==typeof c&&(a=c)}this.extensions=a||{}}toJSON(){return{...this,message:this.message}}toString(){return this.message}get[Symbol.toStringTag](){return"GraphQLError"}}function X(e){return new Y(`Syntax Error: Unexpected token at ${K} in ${e}`)}function Z(e){if(e.lastIndex=K,e.test(Q))return Q.slice(K,K=e.lastIndex)}var ee=/ +(?=[^\s])/y;function te(e){for(var t=e.split("\n"),n="",r=0,i=0,o=t.length-1,s=0;s<t.length;s++)ee.lastIndex=0,ee.test(t[s])&&(s&&(!r||ee.lastIndex<r)&&(r=ee.lastIndex),i=i||s,o=s);for(var a=i;a<=o;a++)a!==i&&(n+="\n"),n+=t[a].slice(r).replace(/\\"""/g,'"""');return n}function ne(){for(var e=0|Q.charCodeAt(K++);9===e||10===e||13===e||32===e||35===e||44===e||65279===e;e=0|Q.charCodeAt(K++))if(35===e)for(;10!==(e=Q.charCodeAt(K++))&&13!==e;);K--}function re(){for(var e=K,t=0|Q.charCodeAt(K++);t>=48&&t<=57||t>=65&&t<=90||95===t||t>=97&&t<=122;t=0|Q.charCodeAt(K++));if(e===K-1)throw X("Name");var n=Q.slice(e,--K);return ne(),n}function ie(){return{kind:"Name",value:re()}}var oe=/(?:"""|(?:[\s\S]*?[^\\])""")/y,se=/(?:(?:\.\d+)?[eE][+-]?\d+|\.\d+)/y;function ae(e){var t;switch(Q.charCodeAt(K)){case 91:K++,ne();for(var n=[];93!==Q.charCodeAt(K);)n.push(ae(e));return K++,ne(),{kind:"ListValue",values:n};case 123:K++,ne();for(var r=[];125!==Q.charCodeAt(K);){var i=ie();if(58!==Q.charCodeAt(K++))throw X("ObjectField");ne(),r.push({kind:"ObjectField",name:i,value:ae(e)})}return K++,ne(),{kind:"ObjectValue",fields:r};case 36:if(e)throw X("Variable");return K++,{kind:"Variable",name:ie()};case 34:if(34===Q.charCodeAt(K+1)&&34===Q.charCodeAt(K+2)){if(K+=3,null==(t=Z(oe)))throw X("StringValue");return ne(),{kind:"StringValue",value:te(t.slice(0,-3)),block:!0}}var o,s=K;K++;var a=!1;for(o=0|Q.charCodeAt(K++);92===o&&(K++,a=!0)||10!==o&&13!==o&&34!==o&&o;o=0|Q.charCodeAt(K++));if(34!==o)throw X("StringValue");return t=Q.slice(s,K),ne(),{kind:"StringValue",value:a?JSON.parse(t):t.slice(1,-1),block:!1};case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:for(var c,l=K++;(c=0|Q.charCodeAt(K++))>=48&&c<=57;);var u=Q.slice(l,--K);if(46===(c=Q.charCodeAt(K))||69===c||101===c){if(null==(t=Z(se)))throw X("FloatValue");return ne(),{kind:"FloatValue",value:u+t}}return ne(),{kind:"IntValue",value:u};case 110:if(117===Q.charCodeAt(K+1)&&108===Q.charCodeAt(K+2)&&108===Q.charCodeAt(K+3))return K+=4,ne(),{kind:"NullValue"};break;case 116:if(114===Q.charCodeAt(K+1)&&117===Q.charCodeAt(K+2)&&101===Q.charCodeAt(K+3))return K+=4,ne(),{kind:"BooleanValue",value:!0};break;case 102:if(97===Q.charCodeAt(K+1)&&108===Q.charCodeAt(K+2)&&115===Q.charCodeAt(K+3)&&101===Q.charCodeAt(K+4))return K+=5,ne(),{kind:"BooleanValue",value:!1}}return{kind:"EnumValue",value:re()}}function ce(e){if(40===Q.charCodeAt(K)){var t=[];K++,ne();do{var n=ie();if(58!==Q.charCodeAt(K++))throw X("Argument");ne(),t.push({kind:"Argument",name:n,value:ae(e)})}while(41!==Q.charCodeAt(K));return K++,ne(),t}}function le(e){if(64===Q.charCodeAt(K)){var t=[];do{K++,t.push({kind:"Directive",name:ie(),arguments:ce(e)})}while(64===Q.charCodeAt(K));return t}}function ue(){for(var e=0;91===Q.charCodeAt(K);)e++,K++,ne();var t={kind:"NamedType",name:ie()};do{if(33===Q.charCodeAt(K)&&(K++,ne(),t={kind:"NonNullType",type:t}),e){if(93!==Q.charCodeAt(K++))throw X("NamedType");ne(),t={kind:"ListType",type:t}}}while(e--);return t}function de(){if(123!==Q.charCodeAt(K++))throw X("SelectionSet");return ne(),he()}function he(){var e=[];do{if(46===Q.charCodeAt(K)){if(46!==Q.charCodeAt(++K)||46!==Q.charCodeAt(++K))throw X("SelectionSet");switch(K++,ne(),Q.charCodeAt(K)){case 64:e.push({kind:"InlineFragment",typeCondition:void 0,directives:le(!1),selectionSet:de()});break;case 111:110===Q.charCodeAt(K+1)?(K+=2,ne(),e.push({kind:"InlineFragment",typeCondition:{kind:"NamedType",name:ie()},directives:le(!1),selectionSet:de()})):e.push({kind:"FragmentSpread",name:ie(),directives:le(!1)});break;case 123:K++,ne(),e.push({kind:"InlineFragment",typeCondition:void 0,directives:void 0,selectionSet:he()});break;default:e.push({kind:"FragmentSpread",name:ie(),directives:le(!1)})}}else{var t=ie(),n=void 0;58===Q.charCodeAt(K)&&(K++,ne(),n=t,t=ie());var r=ce(!1),i=le(!1),o=void 0;123===Q.charCodeAt(K)&&(K++,ne(),o=he()),e.push({kind:"Field",alias:n,name:t,arguments:r,directives:i,selectionSet:o})}}while(125!==Q.charCodeAt(K));return K++,ne(),{kind:"SelectionSet",selections:e}}function fe(){if(ne(),40===Q.charCodeAt(K)){var e=[];K++,ne();do{if(36!==Q.charCodeAt(K++))throw X("Variable");var t=ie();if(58!==Q.charCodeAt(K++))throw X("VariableDefinition");ne();var n=ue(),r=void 0;61===Q.charCodeAt(K)&&(K++,ne(),r=ae(!0)),ne(),e.push({kind:"VariableDefinition",variable:{kind:"Variable",name:t},type:n,defaultValue:r,directives:le(!0)})}while(41!==Q.charCodeAt(K));return K++,ne(),e}}function pe(){var e=ie();if(111!==Q.charCodeAt(K++)||110!==Q.charCodeAt(K++))throw X("FragmentDefinition");return ne(),{kind:"FragmentDefinition",name:e,typeCondition:{kind:"NamedType",name:ie()},directives:le(!1),selectionSet:de()}}function me(){var e=[];do{if(123===Q.charCodeAt(K))K++,ne(),e.push({kind:"OperationDefinition",operation:"query",name:void 0,variableDefinitions:void 0,directives:void 0,selectionSet:he()});else{var t=re();switch(t){case"fragment":e.push(pe());break;case"query":case"mutation":case"subscription":var n,r=void 0;40!==(n=Q.charCodeAt(K))&&64!==n&&123!==n&&(r=ie()),e.push({kind:"OperationDefinition",operation:t,name:r,variableDefinitions:fe(),directives:le(!1),selectionSet:de()});break;default:throw X("Document")}}}while(K<Q.length);return e}function ve(e,t,n){for(var r="",i=0;i<e.length;i++)i&&(r+=t),r+=n(e[i]);return r}var ye="\n",ge={OperationDefinition(e){var t=e.operation;return e.name&&(t+=" "+e.name.value),e.variableDefinitions&&e.variableDefinitions.length&&(e.name||(t+=" "),t+="("+ve(e.variableDefinitions,", ",ge.VariableDefinition)+")"),e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),"query"!==t?t+" "+ge.SelectionSet(e.selectionSet):ge.SelectionSet(e.selectionSet)},VariableDefinition(e){var t=ge.Variable(e.variable)+": "+be(e.type);return e.defaultValue&&(t+=" = "+be(e.defaultValue)),e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),t},Field(e){var t=e.alias?e.alias.value+": "+e.name.value:e.name.value;if(e.arguments&&e.arguments.length){var n=ve(e.arguments,", ",ge.Argument);t.length+n.length+2>80?t+="("+(ye+="  ")+ve(e.arguments,ye,ge.Argument)+(ye=ye.slice(0,-2))+")":t+="("+n+")"}return e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),e.selectionSet&&e.selectionSet.selections.length&&(t+=" "+ge.SelectionSet(e.selectionSet)),t},StringValue:e=>e.block?function(e){return'"""\n'+e.replace(/"""/g,'\\"""')+'\n"""'}(e.value).replace(/\n/g,ye):function(e){return JSON.stringify(e)}(e.value),BooleanValue:e=>""+e.value,NullValue:e=>"null",IntValue:e=>e.value,FloatValue:e=>e.value,EnumValue:e=>e.value,Name:e=>e.value,Variable:e=>"$"+e.name.value,ListValue:e=>"["+ve(e.values,", ",be)+"]",ObjectValue:e=>"{"+ve(e.fields,", ",ge.ObjectField)+"}",ObjectField:e=>e.name.value+": "+be(e.value),Document:e=>e.definitions&&e.definitions.length?ve(e.definitions,"\n\n",be):"",SelectionSet:e=>"{"+(ye+="  ")+ve(e.selections,ye,be)+(ye=ye.slice(0,-2))+"}",Argument:e=>e.name.value+": "+be(e.value),FragmentSpread(e){var t="..."+e.name.value;return e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),t},InlineFragment(e){var t="...";return e.typeCondition&&(t+=" on "+e.typeCondition.name.value),e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),t+" "+ge.SelectionSet(e.selectionSet)},FragmentDefinition(e){var t="fragment "+e.name.value;return t+=" on "+e.typeCondition.name.value,e.directives&&e.directives.length&&(t+=" "+ve(e.directives," ",ge.Directive)),t+" "+ge.SelectionSet(e.selectionSet)},Directive(e){var t="@"+e.name.value;return e.arguments&&e.arguments.length&&(t+="("+ve(e.arguments,", ",ge.Argument)+")"),t},NamedType:e=>e.name.value,ListType:e=>"["+be(e.type)+"]",NonNullType:e=>be(e.type)+"!"},be=e=>ge[e.kind](e);var _e=()=>{},we=_e;function xe(e){return{tag:0,0:e}}function ke(e){return{tag:1,0:e}}var Se=()=>"function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator",Ee=e=>e;function Oe(e){return t=>n=>{var r=we;t(t=>{0===t?n(0):0===t.tag?(r=t[0],n(t)):e(t[0])?n(t):r(0)})}}function Ce(e){return t=>n=>t(t=>{0===t||0===t.tag?n(t):n(ke(e(t[0])))})}function Re(e){return t=>n=>{var r=[],i=we,o=!1,s=!1;t(t=>{var a,c;s||(0===t?(s=!0,r.length||n(0)):0===t.tag?i=t[0]:(o=!1,a=e(t[0]),c=we,a(e=>{if(0===e){if(r.length){var t=r.indexOf(c);t>-1&&(r=r.slice()).splice(t,1),r.length||(s?n(0):o||(o=!0,i(0)))}}else 0===e.tag?(r.push(c=e[0]),c(0)):r.length&&(n(e),c(0))}),o||(o=!0,i(0))))}),n(xe(e=>{if(1===e){s||(s=!0,i(1));for(var t=0,n=r,a=r.length;t<a;t++)n[t](1);r.length=0}else{s||o?o=!1:(o=!0,i(0));for(var c=0,l=r,u=r.length;c<u;c++)l[c](0)}}))}}function Ae(e){return function(e){return Re(Ee)(e)}(Le(e))}function Ne(e){return t=>n=>{var r=!1;t(t=>{if(r);else if(0===t)r=!0,n(0),e();else if(0===t.tag){var i=t[0];n(xe(t=>{1===t?(r=!0,i(1),e()):i(t)}))}else n(t)})}}function Pe(e){return t=>n=>t(t=>{0===t?n(0):0===t.tag?(n(t),e()):n(t)})}function Te(e){var t=[],n=we,r=!1;return i=>{t.push(i),1===t.length&&e(e=>{if(0===e){for(var i=0,o=t,s=t.length;i<s;i++)o[i](0);t.length=0}else if(0===e.tag)n=e[0];else{r=!1;for(var a=0,c=t,l=t.length;a<l;a++)c[a](e)}}),i(xe(e=>{if(1===e){var o=t.indexOf(i);o>-1&&(t=t.slice()).splice(o,1),t.length||n(1)}else r||(r=!0,n(0))}))}}function Me(e){return t=>n=>{var r=we,i=we,o=!1,s=!1,a=!1,c=!1;t(t=>{var l;c||(0===t?(c=!0,a||n(0)):0===t.tag?r=t[0]:(a&&(i(1),i=we),o?o=!1:(o=!0,r(0)),l=e(t[0]),a=!0,l(e=>{a&&(0===e?(a=!1,c?n(0):o||(o=!0,r(0))):0===e.tag?(s=!1,(i=e[0])(0)):(n(e),s?s=!1:i(0)))})))}),n(xe(e=>{1===e?(c||(c=!0,r(1)),a&&(a=!1,i(1))):(c||o||(o=!0,r(0)),a&&!s&&(s=!0,i(0)))}))}}function je(e){return t=>n=>{var r=we,i=!1,o=0;t(t=>{i||(0===t?(i=!0,n(0)):0===t.tag?r=t[0]:o++<e?(n(t),!i&&o>=e&&(i=!0,n(0),r(1))):n(t))}),n(xe(t=>{1!==t||i?0===t&&!i&&o<e&&r(0):(i=!0,r(1))}))}}function Fe(e){return t=>n=>{var r=we,i=we,o=!1;t(t=>{o||(0===t?(o=!0,i(1),n(0)):0===t.tag?(r=t[0],e(e=>{0===e||(0===e.tag?(i=e[0])(0):(o=!0,i(1),r(1),n(0)))})):n(t))}),n(xe(e=>{1!==e||o?o||r(0):(o=!0,r(1),i(1))}))}}function De(e){return t=>{var n,r=e[Se()]&&e[Se()]()||e,i=!1,o=!1,s=!1;t(xe(async e=>{if(1===e)i=!0,r.return&&r.return();else if(o)s=!0;else{for(s=o=!0;s&&!i;)if((n=await r.next()).done)i=!0,r.return&&await r.return(),t(0);else try{s=!1,t(ke(n.value))}catch(a){if(!r.throw)throw a;(i=!!(await r.throw(a)).done)&&t(0)}o=!1}}))}}var Le=function(e){return e[Symbol.asyncIterator]?De(e):t=>{var n,r=e[Symbol.iterator](),i=!1,o=!1,s=!1;t(xe(e=>{if(1===e)i=!0,r.return&&r.return();else if(o)s=!0;else{for(s=o=!0;s&&!i;)if((n=r.next()).done)i=!0,r.return&&r.return(),t(0);else try{s=!1,t(ke(n.value))}catch(a){if(!r.throw)throw a;(i=!!r.throw(a).done)&&t(0)}o=!1}}))}};function Ie(e){return t=>{var n=!1;t(xe(r=>{1===r?n=!0:n||(n=!0,t(ke(e)),t(0))}))}}function qe(e){return t=>{var n=!1,r=e({next(e){n||t(ke(e))},complete(){n||(n=!0,t(0))}});t(xe(e=>{1!==e||n||(n=!0,r())}))}}function Ue(e){return t=>{var n=we,r=!1;return t(t=>{0===t?r=!0:0===t.tag?(n=t[0])(0):r||(e(t[0]),n(0))}),{unsubscribe(){r||(r=!0,n(1))}}}}var ze=e=>e&&"string"==typeof e.message&&(e.extensions||"GraphQLError"===e.name)?e:"object"==typeof e&&"string"==typeof e.message?new Y(e.message,e.nodes,e.source,e.positions,e.path,e,e.extensions||{}):new Y(e);class Be extends Error{constructor(e){var t=(e.graphQLErrors||[]).map(ze),n=((e,t)=>{var n="";if(e)return`[Network] ${e.message}`;if(t)for(var r=0,i=t.length;r<i;r++)n&&(n+="\n"),n+=`[GraphQL] ${t[r].message}`;return n})(e.networkError,t);super(n),this.name="CombinedError",this.message=n,this.graphQLErrors=t,this.networkError=e.networkError,this.response=e.response}toString(){return this.message}}var He=(e,t)=>{for(var n=0|(t||5381),r=0,i=0|e.length;r<i;r++)n=(n<<5)+n+e.charCodeAt(r);return n},We=new Set,$e=new WeakMap,Qe=(e,t)=>{if(null===e||We.has(e))return"null";if("object"!=typeof e)return JSON.stringify(e)||"";if(e.toJSON)return Qe(e.toJSON(),t);if(Array.isArray(e)){for(var n="[",r=0,i=e.length;r<i;r++)n.length>1&&(n+=","),n+=Qe(e[r],t)||"null";return n+"]"}if(!t&&(Je!==Ge&&e instanceof Je||Ye!==Ge&&e instanceof Ye))return"null";var o=Object.keys(e).sort();if(!o.length&&e.constructor&&Object.getPrototypeOf(e).constructor!==Object.prototype.constructor){var s=$e.get(e)||Math.random().toString(36).slice(2);return $e.set(e,s),Qe({__key:s},t)}We.add(e);for(var a="{",c=0,l=o.length;c<l;c++){var u=Qe(e[o[c]],t);u&&(a.length>1&&(a+=","),a+=Qe(o[c],t)+":"+u)}return We.delete(e),a+"}"},Ke=(e,t,n)=>{if(null==n||"object"!=typeof n||n.toJSON||We.has(n));else if(Array.isArray(n))for(var r=0,i=n.length;r<i;r++)Ke(e,`${t}.${r}`,n[r]);else if(n instanceof Je||n instanceof Ye)e.set(t,n);else for(var o in We.add(n),n)Ke(e,`${t}.${o}`,n[o])},Ve=(e,t)=>(We.clear(),Qe(e,t||!1));class Ge{}var Je="undefined"!=typeof File?File:Ge,Ye="undefined"!=typeof Blob?Blob:Ge,Xe=/("{3}[\s\S]*"{3}|"(?:\\.|[^"])*")/g,Ze=/(?:#[^\n\r]+)?(?:[\r\n]+|$)/g,et=(e,t)=>t%2==0?e.replace(Ze,"\n"):e,tt=e=>e.split(Xe).map(et).join("").trim(),nt=new Map,rt=new Map,it=e=>{var t;return"string"==typeof e?t=tt(e):e.loc&&rt.get(e.__key)===e?t=e.loc.source.body:(t=nt.get(e)||tt(function(e){return ye="\n",ge[e.kind]?ge[e.kind](e):""}(e)),nt.set(e,t)),"string"==typeof e||e.loc||(e.loc={start:0,end:t.length,source:{body:t,name:"gql",locationOffset:{line:1,column:1}}}),t},ot=e=>{var t;if(e.documentId)t=He(e.documentId);else if(t=He(it(e)),e.definitions){var n=ct(e);n&&(t=He(`\n# ${n}`,t))}return t},st=e=>{var t,n;return"string"==typeof e?(t=ot(e),n=rt.get(t)||function(e,t){return Q=e.body?e.body:e,K=0,ne(),t&&t.noLocation?{kind:"Document",definitions:me()}:{kind:"Document",definitions:me(),loc:{start:0,end:Q.length,startToken:void 0,endToken:void 0,source:{body:Q,name:"graphql.web",locationOffset:{line:1,column:1}}}}}(e,{noLocation:!0})):(t=e.__key||ot(e),n=rt.get(t)||e),n.loc||it(n),n.__key=t,rt.set(t,n),n},at=(e,t,n)=>{var r=t||{},i=st(e),o=Ve(r,!0),s=i.__key;return"{}"!==o&&(s=He(o,s)),{key:s,query:i,variables:r,extensions:n}},ct=e=>{for(var t=0,n=e.definitions.length;t<n;t++){var r=e.definitions[t];if(r.kind===G)return r.name?r.name.value:void 0}},lt=(e,t,n)=>{if(!("data"in t||"errors"in t&&Array.isArray(t.errors)))throw new Error("No Content");var r="subscription"===e.kind;return{operation:e,data:t.data,error:Array.isArray(t.errors)?new Be({graphQLErrors:t.errors,response:n}):void 0,extensions:t.extensions?{...t.extensions}:void 0,hasNext:null==t.hasNext?r:t.hasNext,stale:!1}},ut=(e,t)=>{if("object"==typeof e&&null!=e){if(Array.isArray(e)){e=[...e];for(var n=0,r=t.length;n<r;n++)e[n]=ut(e[n],t[n]);return e}if(!e.constructor||e.constructor===Object){for(var i in e={...e},t)e[i]=ut(e[i],t[i]);return e}}return t},dt=(e,t,n,r)=>{var i=e.error?e.error.graphQLErrors:[],o=!!e.extensions||!!(t.payload||t).extensions,s={...e.extensions,...(t.payload||t).extensions},a=t.incremental;"path"in t&&(a=[t]);var c={data:e.data};if(a)for(var l=function(){var e=a[u];Array.isArray(e.errors)&&i.push(...e.errors),e.extensions&&(Object.assign(s,e.extensions),o=!0);var t="data",n=c,l=[];if(e.path)l=e.path;else if(r){var d=r.find(t=>t.id===e.id);l=e.subPath?[...d.path,...e.subPath]:d.path}for(var h=0,f=l.length;h<f;t=l[h++])n=n[t]=Array.isArray(n[t])?[...n[t]]:{...n[t]};if(e.items)for(var p=+t>=0?t:0,m=0,v=e.items.length;m<v;m++)n[p+m]=ut(n[p+m],e.items[m]);else void 0!==e.data&&(n[t]=ut(n[t],e.data))},u=0,d=a.length;u<d;u++)l();else c.data=(t.payload||t).data||e.data,i=t.errors||t.payload&&t.payload.errors||i;return{operation:e.operation,data:c.data,error:i.length?new Be({graphQLErrors:i,response:n}):void 0,extensions:o?s:void 0,hasNext:null!=t.hasNext?t.hasNext:e.hasNext,stale:!1}};var ht=e=>{var t=e.indexOf("?");return t>-1?[e.slice(0,t),new URLSearchParams(e.slice(t+1))]:[e,new URLSearchParams]},ft=(e,t)=>{var n,r={accept:"subscription"===e.kind?"text/event-stream, multipart/mixed":"application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed"},i=("function"==typeof e.context.fetchOptions?e.context.fetchOptions():e.context.fetchOptions)||{};if(i.headers)if("has"in(n=i.headers)&&!Object.keys(n).length)i.headers.forEach((e,t)=>{r[t]=e});else if(Array.isArray(i.headers))i.headers.forEach((e,t)=>{Array.isArray(e)?r[e[0]]?r[e[0]]=`${r[e[0]]},${e[1]}`:r[e[0]]=e[1]:r[t]=e});else for(var o in i.headers)r[o.toLowerCase()]=i.headers[o];var s=((e,t)=>{if(t&&("query"!==e.kind||!e.context.preferGetMethod)){var n=Ve(t),r=(a=t.variables,c=new Map,Je===Ge&&Ye===Ge||(We.clear(),Ke(c,"variables",a)),c);if(r.size){var i=new FormData;i.append("operations",n),i.append("map",Ve({...[...r.keys()].map(e=>[e])}));var o=0;for(var s of r.values())i.append(""+o++,s);return i}return n}var a,c})(e,t);return"string"!=typeof s||r["content-type"]||(r["content-type"]="application/json"),{...i,method:s?"POST":"GET",body:s,headers:r}},pt=/boundary="?([^=";]+)"?/i,mt=/data: ?([^\n]+)/;async function*vt(e){if(e.body[Symbol.asyncIterator])for await(var t of e.body)yield t;else{var n,r=e.body.getReader();try{for(;!(n=await r.read()).done;)yield n.value}finally{r.cancel()}}}async function*yt(e,t){var n,r="undefined"!=typeof TextDecoder?new TextDecoder:null,i="";for await(var o of e)for(i+="Buffer"===o.constructor.name?o.toString():r.decode(o,{stream:!0});(n=i.indexOf(t))>-1;)yield i.slice(0,n),i=i.slice(n+t.length)}async function*gt(e,t,n){var r,i=!0,o=null;try{yield await Promise.resolve();var s,a=(r=await(e.context.fetch||fetch)(t,n)).headers.get("Content-Type")||"";for await(var c of/multipart\/mixed/i.test(a)?async function*(e,t){var n,r=e.match(pt),i="--"+(r?r[1]:"-"),o=!0;for await(var s of yt(vt(t),"\r\n"+i)){if(o){o=!1;var a=s.indexOf(i);if(!(a>-1))continue;s=s.slice(a+i.length)}try{yield n=JSON.parse(s.slice(s.indexOf("\r\n\r\n")+4))}catch(c){if(!n)throw c}if(n&&!1===n.hasNext)break}n&&!1!==n.hasNext&&(yield{hasNext:!1})}(a,r):/text\/event-stream/i.test(a)?async function*(e){var t;for await(var n of yt(vt(e),"\n\n")){var r=n.match(mt);if(r){var i=r[1];try{yield t=JSON.parse(i)}catch(o){if(!t)throw o}if(t&&!1===t.hasNext)break}}t&&!1!==t.hasNext&&(yield{hasNext:!1})}(r):/text\//i.test(a)?async function*(e){var t=await e.text();try{var n=JSON.parse(t);0,yield n}catch(r){throw new Error(t)}}(r):async function*(e){yield JSON.parse(await e.text())}(r))c.pending&&!o?s=c.pending:c.pending&&(s=[...s,...c.pending]),o=o?dt(o,c,r,s):lt(e,c,r),i=!1,yield o,i=!0;o||(yield o=lt(e,{},r))}catch(l){if(!i)throw l;yield((e,t,n)=>({operation:e,data:void 0,error:new Be({networkError:t,response:n}),extensions:void 0,hasNext:!1,stale:!1}))(e,r&&(r.status<200||r.status>=300)&&r.statusText?new Error(r.statusText):l,r)}}function bt(e){var t=t=>e(t);return t.toPromise=()=>{return e=je(1)(Oe(e=>!e.stale&&!e.hasNext)(t)),new Promise(t=>{var n,r=we;e(e=>{0===e?Promise.resolve(n).then(t):0===e.tag?(r=e[0])(0):(n=e[0],r(0))})});var e},t.then=(e,n)=>t.toPromise().then(e,n),t.subscribe=e=>Ue(e)(t),t}function _t(e,t,n){return{...t,kind:e,context:t.context?{...t.context,...n}:n||t.context}}var wt=()=>{};var xt,kt,St,Et,Ot=({dispatchDebug:e})=>e=>Oe(e=>!1)(e),Ct=function e(t){var n=0,r=new Map,i=new Map,o=new Set,s=[],a={url:t.url,fetchSubscriptions:t.fetchSubscriptions,fetchOptions:t.fetchOptions,fetch:t.fetch,preferGetMethod:t.preferGetMethod,requestPolicy:t.requestPolicy||"cache-first"},c=function(){var e,t;return{source:Te(qe(n=>(e=n.next,t=n.complete,_e))),next(t){e&&e(t)},complete(){t&&t()}}}();function l(e){"mutation"!==e.kind&&"teardown"!==e.kind&&o.has(e.key)||("teardown"===e.kind?o.delete(e.key):"mutation"!==e.kind&&o.add(e.key),c.next(e))}var u=!1;function d(e){if(e&&l(e),!u){for(u=!0;u&&(e=s.shift());)l(e);u=!1}}var h=e=>{var t=Fe(Oe(t=>"teardown"===t.kind&&t.key===e.key)(c.source))(Oe(t=>t.operation.kind===e.kind&&t.operation.key===e.key&&(!t.operation.context._instance||t.operation.context._instance===e.context._instance))(y));return t="query"!==e.kind?function(e){return t=>n=>{var r=we,i=!1;t(t=>{i||(0===t?(i=!0,n(0)):0===t.tag?(r=t[0],n(t)):e(t[0])?n(t):(i=!0,n(t),n(0),r(1)))})}}(e=>!!e.hasNext)(t):Me(t=>{var n=Ie(t);return t.stale||t.hasNext?n:Ae([n,Ce(()=>(t.stale=!0,t))(je(1)(Oe(t=>t.key===e.key)(c.source)))])})(t),t="mutation"!==e.kind?Ne(()=>{o.delete(e.key),r.delete(e.key),i.delete(e.key),u=!1;for(var t=s.length-1;t>=0;t--)s[t].key===e.key&&s.splice(t,1);l(_t("teardown",e,e.context))})(function(e){return t=>n=>{var r=!1;t(t=>{if(r);else if(0===t)r=!0,n(0);else if(0===t.tag){var i=t[0];n(xe(e=>{1===e&&(r=!0),i(e)}))}else e(t[0]),n(t)})}}(t=>{if(t.stale)if(t.hasNext)for(var n=0;n<s.length;n++){var i=s[n];if(i.key===t.operation.key){o.delete(i.key);break}}else o.delete(e.key);else t.hasNext||o.delete(e.key);r.set(e.key,t)})(t)):Pe(()=>{l(e)})(t),Te(t)},f=this instanceof e?this:Object.create(e.prototype),p=Object.assign(f,{suspense:!!t.suspense,operations$:c.source,reexecuteOperation(e){if("teardown"===e.kind)d(e);else if("mutation"===e.kind)s.push(e),Promise.resolve().then(d);else if(i.has(e.key)){for(var t=!1,n=0;n<s.length;n++)s[n].key===e.key&&(s[n]=e,t=!0);t||o.has(e.key)&&"network-only"!==e.context.requestPolicy?(o.delete(e.key),Promise.resolve().then(d)):(s.push(e),Promise.resolve().then(d))}},createRequestOperation:(e,t,r)=>(r||(r={}),_t(e,t,{_instance:"mutation"===e?n=n+1|0:void 0,...a,...r,requestPolicy:r.requestPolicy||a.requestPolicy,suspense:r.suspense||!1!==r.suspense&&p.suspense})),executeRequestOperation:e=>"mutation"===e.kind?bt(h(e)):bt(function(e){return t=>e()(t)}(()=>{var t=i.get(e.key);t||i.set(e.key,t=h(e)),t=Pe(()=>{d(e)})(t);var n=r.get(e.key);return"query"===e.kind&&n&&(n.stale||n.hasNext)?Me(Ie)(Ae([t,Oe(t=>t===r.get(e.key))(Ie(n))])):t})),executeQuery(e,t){var n=p.createRequestOperation("query",e,t);return p.executeRequestOperation(n)},executeSubscription(e,t){var n=p.createRequestOperation("subscription",e,t);return p.executeRequestOperation(n)},executeMutation(e,t){var n=p.createRequestOperation("mutation",e,t);return p.executeRequestOperation(n)},readQuery(e,t,n){var r=null;return Ue(e=>{r=e})(p.query(e,t,n)).unsubscribe(),r},query:(e,t,n)=>p.executeQuery(at(e,t),n),subscription:(e,t,n)=>p.executeSubscription(at(e,t),n),mutation:(e,t,n)=>p.executeMutation(at(e,t),n)}),m=wt,v=(e=>({client:t,forward:n,dispatchDebug:r})=>e.reduceRight((e,n)=>n({client:t,forward:t=>Te(e(Te(t))),dispatchDebug(e){}}),n))(t.exchanges),y=Te(v({client:p,dispatchDebug:m,forward:Ot({dispatchDebug:m})})(c.source));return function(e){Ue(e=>{})(e)}(y),p},Rt=0,At=[],Nt=t,Pt=Nt.__b,Tt=Nt.__r,Mt=Nt.diffed,jt=Nt.__c,Ft=Nt.unmount,Dt=Nt.__;function Lt(e,t){Nt.__h&&Nt.__h(kt,e,Rt||t),Rt=0;var n=kt.__H||(kt.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function It(e){return Rt=1,qt(tn,e)}function qt(e,t,n){var r=Lt(xt++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):tn(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=kt,!kt.__f)){var i=function(e,t,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter(function(e){return!!e.__c});if(i.every(function(e){return!e.__N}))return!o||o.call(this,e,t,n);var s=r.__c.props!==e;return i.forEach(function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(s=!0)}}),o&&o.call(this,e,t,n)||s};kt.__f=!0;var o=kt.shouldComponentUpdate,s=kt.componentWillUpdate;kt.componentWillUpdate=function(e,t,n){if(this.__e){var r=o;o=void 0,i(e,t,n),o=r}s&&s.call(this,e,t,n)},kt.shouldComponentUpdate=i}return r.__N||r.__}function Ut(e,t){var n=Lt(xt++,3);!Nt.__s&&en(n.__H,t)&&(n.__=e,n.u=t,kt.__H.__h.push(n))}function zt(e,t){var n=Lt(xt++,4);!Nt.__s&&en(n.__H,t)&&(n.__=e,n.u=t,kt.__h.push(n))}function Bt(e){return Rt=5,Wt(function(){return{current:e}},[])}function Ht(e,t,n){Rt=6,zt(function(){if("function"==typeof e){var n=e(t());return function(){e(null),n&&"function"==typeof n&&n()}}if(e)return e.current=t(),function(){return e.current=null}},null==n?n:n.concat(e))}function Wt(e,t){var n=Lt(xt++,7);return en(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function $t(e,t){return Rt=8,Wt(function(){return e},t)}function Qt(e){var t=kt.context[e.__c],n=Lt(xt++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(kt)),t.props.value):e.__}function Kt(e,t){Nt.useDebugValue&&Nt.useDebugValue(t?t(e):e)}function Vt(){var e=Lt(xt++,11);if(!e.__){for(var t=kt.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function Gt(){for(var e;e=At.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Xt),e.__H.__h.forEach(Zt),e.__H.__h=[]}catch(t){e.__H.__h=[],Nt.__e(t,e.__v)}}Nt.__b=function(e){kt=null,Pt&&Pt(e)},Nt.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),Dt&&Dt(e,t)},Nt.__r=function(e){Tt&&Tt(e),xt=0;var t=(kt=e.__c).__H;t&&(St===kt?(t.__h=[],kt.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.u=e.__N=void 0})):(t.__h.forEach(Xt),t.__h.forEach(Zt),t.__h=[],xt=0)),St=kt},Nt.diffed=function(e){Mt&&Mt(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==At.push(t)&&Et===Nt.requestAnimationFrame||((Et=Nt.requestAnimationFrame)||Yt)(Gt)),t.__H.__.forEach(function(e){e.u&&(e.__H=e.u),e.u=void 0})),St=kt=null},Nt.__c=function(e,t){t.some(function(e){try{e.__h.forEach(Xt),e.__h=e.__h.filter(function(e){return!e.__||Zt(e)})}catch(n){t.some(function(e){e.__h&&(e.__h=[])}),t=[],Nt.__e(n,e.__v)}}),jt&&jt(e,t)},Nt.unmount=function(e){Ft&&Ft(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(e){try{Xt(e)}catch(n){t=n}}),n.__H=void 0,t&&Nt.__e(t,n.__v))};var Jt="function"==typeof requestAnimationFrame;function Yt(e){var t,n=function(){clearTimeout(r),Jt&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,35);Jt&&(t=requestAnimationFrame(n))}function Xt(e){var t=kt,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),kt=t}function Zt(e){var t=kt;e.__c=e.__(),kt=t}function en(e,t){return!e||e.length!==t.length||t.some(function(t,n){return t!==e[n]})}function tn(e,t){return"function"==typeof t?t(e):t}function nn(e,t){for(var n in t)e[n]=t[n];return e}function rn(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function on(e,t){var n=t(),r=It({t:{__:n,u:t}}),i=r[0].t,o=r[1];return zt(function(){i.__=n,i.u=t,sn(i)&&o({t:i})},[e,n,t]),Ut(function(){return sn(i)&&o({t:i}),e(function(){sn(i)&&o({t:i})})},[e]),n}function sn(e){var t,n,r=e.u,i=e.__;try{var o=r();return!((t=i)===(n=o)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(s){return!0}}function an(e){e()}function cn(e){return e}function ln(){return[!1,an]}var un=zt;function dn(e,t){this.props=e,this.context=t}(dn.prototype=new x).isPureReactComponent=!0,dn.prototype.shouldComponentUpdate=function(e,t){return rn(this.props,e)||rn(this.state,t)};var hn=t.__b;t.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),hn&&hn(e)};var fn="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function pn(e){function t(t){var n=nn({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=fn,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var mn=function(e,t){return null==e?null:A(A(e).map(t))},vn={map:mn,forEach:mn,count:function(e){return e?A(e).length:0},only:function(e){var t=A(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:A},yn=t.__e;t.__e=function(e,t,n,r){if(e.then)for(var i,o=t;o=o.__;)if((i=o.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);yn(e,t,n,r)};var gn=t.unmount;function bn(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(e){"function"==typeof e.__c&&e.__c()}),e.__c.__H=null),null!=(e=nn({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c.__e=!0,e.__c=null),e.__k=e.__k&&e.__k.map(function(e){return bn(e,t,n)})),e}function _n(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(e){return _n(e,t,n)}),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function wn(){this.__u=0,this.o=null,this.__b=null}function xn(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function kn(){this.i=null,this.l=null}t.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),gn&&gn(e)},(wn.prototype=new x).__c=function(e,t){var n=t.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var i=xn(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=s;var a=function(){if(! --r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=_n(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.o.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(s,s)},wn.prototype.componentWillUnmount=function(){this.o=[]},wn.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=bn(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__a&&b(w,null,e.fallback);return i&&(i.__u&=-33),[b(w,null,t.__a?null:e.children),i]};var Sn=function(e,t,n){if(++n[1]===n[0]&&e.l.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.l.size))for(n=e.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.i=n=n[2]}};function En(e){return this.getChildContext=function(){return e.context},e.children}function On(e){var t=this,n=e.h;if(t.componentWillUnmount=function(){U(null,t.v),t.v=null,t.h=null},t.h&&t.h!==n&&t.componentWillUnmount(),!t.v){for(var r=t.__v;null!==r&&!r.__m&&null!==r.__;)r=r.__;t.h=n,t.v={nodeType:1,parentNode:n,childNodes:[],__k:{__m:r.__m},contains:function(){return!0},insertBefore:function(e,n){this.childNodes.push(e),t.h.insertBefore(e,n)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.h.removeChild(e)}}}U(b(En,{context:t.context},e.__v),t.v)}(kn.prototype=new x).__a=function(e){var t=this,n=xn(t.__v),r=t.l.get(e);return r[0]++,function(i){var o=function(){t.props.revealOrder?(r.push(i),Sn(t,e,r)):i()};n?n(o):o()}},kn.prototype.render=function(e){this.i=null,this.l=new Map;var t=A(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.l.set(t[n],this.i=[1,0,this.i]);return e.children},kn.prototype.componentDidUpdate=kn.prototype.componentDidMount=function(){var e=this;this.l.forEach(function(t,n){Sn(e,n,t)})};var Cn="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Rn=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,An=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,Nn=/[A-Z0-9]/g,Pn="undefined"!=typeof document,Tn=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(x.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var Mn=t.event;function jn(){}function Fn(){return this.cancelBubble}function Dn(){return this.defaultPrevented}t.event=function(e){return Mn&&(e=Mn(e)),e.persist=jn,e.isPropagationStopped=Fn,e.isDefaultPrevented=Dn,e.nativeEvent=e};var Ln,In={enumerable:!1,configurable:!0,get:function(){return this.class}},qn=t.vnode;t.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,r={},i=-1===n.indexOf("-");for(var o in t){var s=t[o];if(!("value"===o&&"defaultValue"in t&&null==s||Pn&&"children"===o&&"noscript"===n||"class"===o||"className"===o)){var a=o.toLowerCase();"defaultValue"===o&&"value"in t&&null==t.value?o="value":"download"===o&&!0===s?s="":"translate"===a&&"no"===s?s=!1:"o"===a[0]&&"n"===a[1]?"ondoubleclick"===a?o="ondblclick":"onchange"!==a||"input"!==n&&"textarea"!==n||Tn(t.type)?"onfocus"===a?o="onfocusin":"onblur"===a?o="onfocusout":An.test(o)&&(o=a):a=o="oninput":i&&Rn.test(o)?o=o.replace(Nn,"-$&").toLowerCase():null===s&&(s=void 0),"oninput"===a&&r[o=a]&&(o="oninputCapture"),r[o]=s}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=A(t.children).forEach(function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)})),"select"==n&&null!=r.defaultValue&&(r.value=A(t.children).forEach(function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value})),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",In)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),e.props=r}(e),e.$$typeof=Cn,qn&&qn(e)};var Un=t.__r;t.__r=function(e){Un&&Un(e),Ln=e.__c};var zn=t.diffed;t.diffed=function(e){zn&&zn(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),Ln=null};function Bn(e){return!!e&&e.$$typeof===Cn}function Hn(e){return Bn(e)?B.apply(null,arguments):e}var Wn=function(e,t){return e(t)},$n={useState:It,useId:Vt,useReducer:qt,useEffect:Ut,useLayoutEffect:zt,useInsertionEffect:un,useTransition:ln,useDeferredValue:cn,useSyncExternalStore:on,startTransition:an,useRef:Bt,useImperativeHandle:Ht,useMemo:Wt,useCallback:$t,useContext:Qt,useDebugValue:Kt,version:"18.3.1",Children:vn,render:function(e,t,n){return null==t.__k&&(t.textContent=""),U(e,t),"function"==typeof n&&n(),e?e.__c:null},hydrate:function(e,t,n){return z(e,t),"function"==typeof n&&n(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(U(null,e),!0)},createPortal:function(e,t){var n=b(On,{__v:e,h:t});return n.containerInfo=t,n},createElement:b,createContext:H,createFactory:function(e){return b.bind(null,e)},cloneElement:Hn,createRef:function(){return{current:null}},Fragment:w,isValidElement:Bn,isElement:Bn,isFragment:function(e){return Bn(e)&&e.type===w},isMemo:function(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")},findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:x,PureComponent:dn,memo:function(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:rn(this.props,e)}function r(t){return this.shouldComponentUpdate=n,b(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:pn,flushSync:Wn,unstable_batchedUpdates:function(e,t){return e(t)},StrictMode:w,Suspense:wn,SuspenseList:kn,lazy:function(e){var t,n,r;function i(i){if(t||(t=e()).then(function(e){n=e.default||e},function(e){r=e}),r)throw r;if(!n)throw t;return b(n,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentDispatcher:{current:{readContext:function(e){return Ln.__n[e.__c].props.value},useCallback:$t,useContext:Qt,useDebugValue:Kt,useDeferredValue:cn,useEffect:Ut,useId:Vt,useImperativeHandle:Ht,useInsertionEffect:un,useLayoutEffect:zt,useMemo:Wt,useReducer:qt,useRef:Bt,useState:It,useSyncExternalStore:on,useTransition:ln}}}},Qn=H({}),Kn=Qn.Provider;Qn.Consumer,Qn.displayName="UrqlContext";const Vn=Ct({url:"https://localhost:5002/api/sdata/graphql",exchanges:[({forward:e,dispatchDebug:t})=>t=>{var n=Re(e=>{var n=function(e){var t={query:void 0,documentId:void 0,operationName:ct(e.query),variables:e.variables||void 0,extensions:e.extensions};return!("documentId"in e.query)||!e.query.documentId||e.query.definitions&&e.query.definitions.length?e.extensions&&e.extensions.persistedQuery&&!e.extensions.persistedQuery.miss||(t.query=it(e.query)):t.documentId=e.query.documentId,t}(e),r=((e,t)=>{var n="query"===e.kind&&e.context.preferGetMethod;if(!n||!t)return e.context.url;var r=ht(e.context.url);for(var i in t){var o=t[i];o&&r[1].set(i,"object"==typeof o?Ve(o):o)}var s=r.join("?");return s.length>2047&&"force"!==n?(e.context.preferGetMethod=!1,e.context.url):s})(e,n),i=ft(e,n),o=Fe(Oe(t=>"teardown"===t.kind&&t.key===e.key)(t))(function(e,t,n){var r;return"undefined"!=typeof AbortController&&(n.signal=(r=new AbortController).signal),Ne(()=>{r&&r.abort()})(Oe(e=>!!e)(De(gt(e,t,n))))}(e,r,i));return o})(Oe(e=>"teardown"!==e.kind&&("subscription"!==e.kind||!!e.context.fetchSubscriptions))(t));return Ae([n,e(Oe(e=>"teardown"===e.kind||"subscription"===e.kind&&!e.context.fetchSubscriptions)(t))])}],fetch:async(e,t)=>fetch(e,{...t,credentials:"include",headers:{...t?.headers||{}}})}),Gn=":root {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n  line-height: 1.5;\r\n  font-weight: 400;\r\n  color-scheme: light;\r\n  color: #131722;\r\n  background-color: #ffffff;\r\n  font-synthesis: none;\r\n  text-rendering: optimizeLegibility;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  --color-border: #e1e3e6;\r\n}\r\n\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  background: #f8f9fa;\r\n  color: #131722;\r\n}\r\n\r\nbutton {\r\n  border: 1px solid transparent;\r\n  padding: 0.6em 1.2em;\r\n  font-size: 1em;\r\n  font-weight: 500;\r\n  font-family: inherit;\r\n  background-color: #ffffff;\r\n  color: #131722;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  border: 1px solid --color-border;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\nbutton:focus,\r\nbutton:focus-visible {\r\n  outline: 2px solid #2962ff;\r\n  outline-offset: 2px;\r\n}\r\n\r\ninput {\r\n  font-family: inherit;\r\n  font-size: 1em;\r\n}\r\n\r\n\r\n.watchlist-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}\r\n\r\n.add-instrument-container {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}\r\n\r\n.root-iframe-widget {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n}",Jn=".add-instrument-container {\r\n  display: flex;\r\n}\r\n\r\n.add-instrument-widget {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.add-instrument-trigger {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  border: 1px solid #e1e3e6;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  padding: 6px 10px;\r\n}\r\n\r\n.add-instrument-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 8px;\r\n}\r\n\r\n.add-instrument-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2px;\r\n  border: 1px solid #e1e3e6;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  padding: 6px 10px;\r\n}",Yn="\r\n\r\n.watchlist-modal {\r\n  background: white;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 6px;\r\n  padding: 24px;\r\n  max-height: 100vh;\r\n}\r\n\r\n.modal-header {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #e1e3e6;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.modal-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n}\r\n\r\n.modal-title h3 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.modal-close {\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  color: #6c757d;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.modal-close:hover {\r\n  background: #e9ecef;\r\n  color: #131722;\r\n}\r\n\r\n.instrument-preview {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 12px 16px;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n}\r\n\r\n.instrument-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.instrument-symbol {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  font-size: 16px;\r\n}\r\n\r\n.instrument-name {\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n}\r\n\r\n.instrument-price {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.price {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  font-size: 16px;\r\n}\r\n\r\n.change {\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.change.positive {\r\n  color: #00c853;\r\n}\r\n\r\n.change.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.modal-content {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6c757d;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 10px 10px 10px 36px;\r\n  border: 1px solid #d1d4dc;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.watchlists-section {\r\n  flex: 1;\r\n  padding-top: 24px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-header h4 {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.create-new-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-new-btn:hover:not(:disabled) {\r\n  color: black;\r\n}\r\n\r\n.create-new-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.watchlists-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.create-watchlist-form {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 16px;\r\n  border: 1px solid #e1e3e6;\r\n  margin-bottom: 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-input {\r\n  flex: 1;\r\n  padding: 4px 12px;\r\n  border: 1px solid #d1d4dc;\r\n  background: white;\r\n  color: #131722;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.create-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.create-actions {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.confirm-btn,\r\n.cancel-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.confirm-btn,\r\nbutton.cancel-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  padding: 0;\r\n}\r\n\r\n.confirm-btn {\r\n  border-radius: 4px;\r\n  background: #00c853;\r\n  color: white;\r\n}\r\n\r\n.confirm-btn:hover:not(:disabled) {\r\n  background: #05f168;\r\n}\r\n\r\n.cancel-btn {\r\n  border-radius: 4px;\r\n  background: #6c757d;\r\n  color: white;\r\n}\r\n\r\n.cancel-btn:hover:not(:disabled) {\r\n  background: #333;\r\n}\r\n\r\n.watchlist-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 12px;\r\n  cursor: pointer;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  transition: all 0.2s;\r\n  border-radius: 4px;\r\n}\r\n\r\n.watchlist-item:hover {\r\n  background: #f8f9fa;\r\n  box-shadow: 0 2px 4px rgba(41, 98, 255, 0.1);\r\n}\r\n\r\n.watchlist-info {\r\n  flex: 1;\r\n}\r\n\r\n.watchlist-name {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-weight: 500;\r\n  color: #131722;\r\n  font-size: 16px;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.default-star {\r\n  color: #ffc107;\r\n}\r\n\r\n.watchlist-count {\r\n  color: #6c757d;\r\n  font-size: 14px;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  cursor: pointer;\r\n  color: #333;\r\n  transition: all 0.2s;\r\n}\r\n\r\nbutton.add-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  padding: 0;\r\n}\r\n\r\n.add-btn:hover:not(:disabled) {\r\n  background: #e1e3e6;\r\n}\r\n\r\n.add-btn:disabled {\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  color: #6c757d;\r\n}\r\n\r\n.empty-state p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n}\r\n\r\n/* Loading Spinner */\r\n.loading-spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid #f3f3f3;\r\n  border-top: 2px solid #2962ff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.loading-spinner.small {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.border-radius {\r\n  border-radius: 6px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n\r\n.watchlist-item-skeleton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 10px 12px;\r\n  height: 56px;\r\n  cursor: pointer;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  transition: all 0.2s;\r\n  border-radius: 4px;\r\n}\r\n\r\n.watchlist-info-skeleton {\r\n  flex: 1;\r\n}\r\n\r\n.add-btn-skeleton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  cursor: pointer;\r\n  color: #333;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.watchlist-item-disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}";class Xn{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){const t={listener:e};return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}const Zn="undefined"==typeof window||"Deno"in window;function er(){}function tr(e){return"number"==typeof e&&e>=0&&e!==1/0}function nr(e,t){return Math.max(e+(t||0)-Date.now(),0)}function rr(e,t,n){return vr(e)?"function"==typeof t?{...n,queryKey:e,queryFn:t}:{...t,queryKey:e}:e}function ir(e,t,n){return vr(e)?[{...t,queryKey:e},n]:[e||{},t]}function or(e,t){const{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:s,stale:a}=e;if(vr(s))if(r){if(t.queryHash!==ar(s,t.options))return!1}else if(!lr(t.queryKey,s))return!1;if("all"!==n){const e=t.isActive();if("active"===n&&!e)return!1;if("inactive"===n&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&((void 0===i||i===t.state.fetchStatus)&&!(o&&!o(t)))}function sr(e,t){const{exact:n,fetching:r,predicate:i,mutationKey:o}=e;if(vr(o)){if(!t.options.mutationKey)return!1;if(n){if(cr(t.options.mutationKey)!==cr(o))return!1}else if(!lr(t.options.mutationKey,o))return!1}return("boolean"!=typeof r||"loading"===t.state.status===r)&&!(i&&!i(t))}function ar(e,t){return((null==t?void 0:t.queryKeyHashFn)||cr)(e)}function cr(e){return JSON.stringify(e,(e,t)=>pr(t)?Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{}):t)}function lr(e,t){return ur(e,t)}function ur(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some(n=>!ur(e[n],t[n])))}function dr(e,t){if(e===t)return e;const n=fr(e)&&fr(t);if(n||pr(e)&&pr(t)){const r=n?e.length:Object.keys(e).length,i=n?t:Object.keys(t),o=i.length,s=n?[]:{};let a=0;for(let c=0;c<o;c++){const r=n?c:i[c];s[r]=dr(e[r],t[r]),s[r]===e[r]&&a++}return r===o&&a===r?e:s}return t}function hr(e,t){if(e&&!t||t&&!e)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function fr(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function pr(e){if(!mr(e))return!1;const t=e.constructor;if(void 0===t)return!0;const n=t.prototype;return!!mr(n)&&!!n.hasOwnProperty("isPrototypeOf")}function mr(e){return"[object Object]"===Object.prototype.toString.call(e)}function vr(e){return Array.isArray(e)}function yr(e){return new Promise(t=>{setTimeout(t,e)})}function gr(e){yr(0).then(e)}function br(e,t,n){return null!=n.isDataEqual&&n.isDataEqual(e,t)?e:"function"==typeof n.structuralSharing?n.structuralSharing(e,t):!1!==n.structuralSharing?dr(e,t):t}const _r=new class extends Xn{constructor(){super(),this.setup=e=>{if(!Zn&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),window.addEventListener("focus",t,!1),()=>{window.removeEventListener("visibilitychange",t),window.removeEventListener("focus",t)}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)}setEventListener(e){var t;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.focused!==e&&(this.focused=e,this.onFocus())}onFocus(){this.listeners.forEach(({listener:e})=>{e()})}isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)}},wr=["online","offline"];const xr=new class extends Xn{constructor(){super(),this.setup=e=>{if(!Zn&&window.addEventListener){const t=()=>e();return wr.forEach(e=>{window.addEventListener(e,t,!1)}),()=>{wr.forEach(e=>{window.removeEventListener(e,t)})}}}}onSubscribe(){this.cleanup||this.setEventListener(this.setup)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)}setEventListener(e){var t;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(e=>{"boolean"==typeof e?this.setOnline(e):this.onOnline()})}setOnline(e){this.online!==e&&(this.online=e,this.onOnline())}onOnline(){this.listeners.forEach(({listener:e})=>{e()})}isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine}};function kr(e){return Math.min(1e3*2**e,3e4)}function Sr(e){return"online"!==(null!=e?e:"online")||xr.isOnline()}class Er{constructor(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent}}function Or(e){return e instanceof Er}function Cr(e){let t,n,r,i=!1,o=0,s=!1;const a=new Promise((e,t)=>{n=e,r=t}),c=()=>!_r.isFocused()||"always"!==e.networkMode&&!xr.isOnline(),l=r=>{s||(s=!0,null==e.onSuccess||e.onSuccess(r),null==t||t(),n(r))},u=n=>{s||(s=!0,null==e.onError||e.onError(n),null==t||t(),r(n))},d=()=>new Promise(n=>{t=e=>{const t=s||!c();return t&&n(e),t},null==e.onPause||e.onPause()}).then(()=>{t=void 0,s||null==e.onContinue||e.onContinue()}),h=()=>{if(s)return;let t;try{t=e.fn()}catch(n){t=Promise.reject(n)}Promise.resolve(t).then(l).catch(t=>{var n,r;if(s)return;const a=null!=(n=e.retry)?n:3,l=null!=(r=e.retryDelay)?r:kr,f="function"==typeof l?l(o,t):l,p=!0===a||"number"==typeof a&&o<a||"function"==typeof a&&a(o,t);!i&&p?(o++,null==e.onFail||e.onFail(o,t),yr(f).then(()=>{if(c())return d()}).then(()=>{i?u(t):h()})):u(t)})};return Sr(e.networkMode)?h():d().then(h),{promise:a,cancel:t=>{s||(u(new Er(t)),null==e.abort||e.abort())},continue:()=>(null==t?void 0:t())?a:Promise.resolve(),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1}}}const Rr=console;const Ar=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()};const i=r=>{t?e.push(r):gr(()=>{n(r)})},o=()=>{const t=e;e=[],t.length&&gr(()=>{r(()=>{t.forEach(e=>{n(e)})})})};return{batch:e=>{let n;t++;try{n=e()}finally{t--,t||o()}return n},batchCalls:e=>(...t)=>{i(()=>{e(...t)})},schedule:i,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e}}}();class Nr{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),tr(this.cacheTime)&&(this.gcTimeout=setTimeout(()=>{this.optionalRemove()},this.cacheTime))}updateCacheTime(e){this.cacheTime=Math.max(this.cacheTime||0,null!=e?e:Zn?1/0:3e5)}clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)}}class Pr extends Nr{constructor(e){super(),this.abortSignalConsumed=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.logger=e.logger||Rr,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?null!=r?r:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"loading",fetchStatus:"idle"}}(this.options),this.state=this.initialState,this.scheduleGc()}get meta(){return this.options.meta}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.cache.remove(this)}setData(e,t){const n=br(this.state.data,e,this.options);return this.dispatch({data:n,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt,manual:null==t?void 0:t.manual}),n}setState(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})}cancel(e){var t;const n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(er).catch(er):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.initialState)}isActive(){return this.observers.some(e=>!1!==e.options.enabled)}isDisabled(){return this.getObserversCount()>0&&!this.isActive()}isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(e=>e.getCurrentResult().isStale)}isStaleByTime(e=0){return this.state.isInvalidated||!this.state.dataUpdatedAt||!nr(this.state.dataUpdatedAt,e)}onFocus(){var e;const t=this.observers.find(e=>e.shouldFetchOnWindowFocus());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}onOnline(){var e;const t=this.observers.find(e=>e.shouldFetchOnReconnect());t&&t.refetch({cancelRefetch:!1}),null==(e=this.retryer)||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.retryer&&(this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.scheduleGc()),this.cache.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})}fetch(e,t){var n,r;if("idle"!==this.state.fetchStatus)if(this.state.dataUpdatedAt&&null!=t&&t.cancelRefetch)this.cancel({silent:!0});else if(this.promise){var i;return null==(i=this.retryer)||i.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}Array.isArray(this.options.queryKey);const o=function(){if("function"==typeof AbortController)return new AbortController}(),s={queryKey:this.queryKey,pageParam:void 0,meta:this.meta},a=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>{if(o)return this.abortSignalConsumed=!0,o.signal}})};a(s);const c={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>this.options.queryFn?(this.abortSignalConsumed=!1,this.options.queryFn(s)):Promise.reject("Missing queryFn for queryKey '"+this.options.queryHash+"'")};var l;(a(c),null==(n=this.options.behavior)||n.onFetch(c),this.revertState=this.state,"idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(r=c.fetchOptions)?void 0:r.meta))&&this.dispatch({type:"fetch",meta:null==(l=c.fetchOptions)?void 0:l.meta});const u=e=>{var t,n,r,i;(Or(e)&&e.silent||this.dispatch({type:"error",error:e}),Or(e))||(null==(t=(n=this.cache.config).onError)||t.call(n,e,this),null==(r=(i=this.cache.config).onSettled)||r.call(i,this.state.data,e,this));this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.retryer=Cr({fn:c.fetchFn,abort:null==o?void 0:o.abort.bind(o),onSuccess:e=>{var t,n,r,i;void 0!==e?(this.setData(e),null==(t=(n=this.cache.config).onSuccess)||t.call(n,e,this),null==(r=(i=this.cache.config).onSettled)||r.call(i,e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1):u(new Error(this.queryHash+" data is undefined"))},onError:u,onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode}),this.promise=this.retryer.promise,this.promise}dispatch(e){this.state=(t=>{var n,r;switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null!=(n=e.meta)?n:null,fetchStatus:Sr(this.options.networkMode)?"fetching":"paused",...!t.dataUpdatedAt&&{error:null,status:"loading"}};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(r=e.dataUpdatedAt)?r:Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=e.error;return Or(i)&&i.revert&&this.revertState?{...this.revertState}:{...t,error:i,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),Ar.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate(e)}),this.cache.notify({query:this,type:"updated",action:e})})}}class Tr extends Xn{constructor(e){super(),this.config=e||{},this.queries=[],this.queriesMap={}}build(e,t,n){var r;const i=t.queryKey,o=null!=(r=t.queryHash)?r:ar(i,t);let s=this.get(o);return s||(s=new Pr({cache:this,logger:e.getLogger(),queryKey:i,queryHash:o,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(i)}),this.add(s)),s}add(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"added",query:e}))}remove(e){const t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter(t=>t!==e),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"removed",query:e}))}clear(){Ar.batch(()=>{this.queries.forEach(e=>{this.remove(e)})})}get(e){return this.queriesMap[e]}getAll(){return this.queries}find(e,t){const[n]=ir(e,t);return void 0===n.exact&&(n.exact=!0),this.queries.find(e=>or(n,e))}findAll(e,t){const[n]=ir(e,t);return Object.keys(n).length>0?this.queries.filter(e=>or(n,e)):this.queries}notify(e){Ar.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}onFocus(){Ar.batch(()=>{this.queries.forEach(e=>{e.onFocus()})})}onOnline(){Ar.batch(()=>{this.queries.forEach(e=>{e.onOnline()})})}}class Mr extends Nr{constructor(e){super(),this.defaultOptions=e.defaultOptions,this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.logger=e.logger||Rr,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options={...this.defaultOptions,...e},this.updateCacheTime(this.options.cacheTime)}get meta(){return this.options.meta}setState(e){this.dispatch({type:"setState",state:e})}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.mutationCache.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.observers=this.observers.filter(t=>t!==e),this.scheduleGc(),this.mutationCache.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.observers.length||("loading"===this.state.status?this.scheduleGc():this.mutationCache.remove(this))}continue(){var e,t;return null!=(e=null==(t=this.retryer)?void 0:t.continue())?e:this.execute()}async execute(){const e=()=>{var e;return this.retryer=Cr({fn:()=>this.options.mutationFn?this.options.mutationFn(this.state.variables):Promise.reject("No mutationFn found"),onFail:(e,t)=>{this.dispatch({type:"failed",failureCount:e,error:t})},onPause:()=>{this.dispatch({type:"pause"})},onContinue:()=>{this.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode}),this.retryer.promise},t="loading"===this.state.status;try{var n,r,i,o,s,a,c,l;if(!t){var u,d,h,f;this.dispatch({type:"loading",variables:this.options.variables}),await(null==(u=(d=this.mutationCache.config).onMutate)?void 0:u.call(d,this.state.variables,this));const e=await(null==(h=(f=this.options).onMutate)?void 0:h.call(f,this.state.variables));e!==this.state.context&&this.dispatch({type:"loading",context:e,variables:this.state.variables})}const p=await e();return await(null==(n=(r=this.mutationCache.config).onSuccess)?void 0:n.call(r,p,this.state.variables,this.state.context,this)),await(null==(i=(o=this.options).onSuccess)?void 0:i.call(o,p,this.state.variables,this.state.context)),await(null==(s=(a=this.mutationCache.config).onSettled)?void 0:s.call(a,p,null,this.state.variables,this.state.context,this)),await(null==(c=(l=this.options).onSettled)?void 0:c.call(l,p,null,this.state.variables,this.state.context)),this.dispatch({type:"success",data:p}),p}catch(x){try{var p,m,v,y,g,b,_,w;throw await(null==(p=(m=this.mutationCache.config).onError)?void 0:p.call(m,x,this.state.variables,this.state.context,this)),await(null==(v=(y=this.options).onError)?void 0:v.call(y,x,this.state.variables,this.state.context)),await(null==(g=(b=this.mutationCache.config).onSettled)?void 0:g.call(b,void 0,x,this.state.variables,this.state.context,this)),await(null==(_=(w=this.options).onSettled)?void 0:_.call(w,void 0,x,this.state.variables,this.state.context)),x}finally{this.dispatch({type:"error",error:x})}}}dispatch(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"loading":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:!Sr(this.options.networkMode),status:"loading",variables:e.variables};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"};case"setState":return{...t,...e.state}}})(this.state),Ar.batch(()=>{this.observers.forEach(t=>{t.onMutationUpdate(e)}),this.mutationCache.notify({mutation:this,type:"updated",action:e})})}}class jr extends Xn{constructor(e){super(),this.config=e||{},this.mutations=[],this.mutationId=0}build(e,t,n){const r=new Mr({mutationCache:this,logger:e.getLogger(),mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0});return this.add(r),r}add(e){this.mutations.push(e),this.notify({type:"added",mutation:e})}remove(e){this.mutations=this.mutations.filter(t=>t!==e),this.notify({type:"removed",mutation:e})}clear(){Ar.batch(()=>{this.mutations.forEach(e=>{this.remove(e)})})}getAll(){return this.mutations}find(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find(t=>sr(e,t))}findAll(e){return this.mutations.filter(t=>sr(e,t))}notify(e){Ar.batch(()=>{this.listeners.forEach(({listener:t})=>{t(e)})})}resumePausedMutations(){var e;return this.resuming=(null!=(e=this.resuming)?e:Promise.resolve()).then(()=>{const e=this.mutations.filter(e=>e.state.isPaused);return Ar.batch(()=>e.reduce((e,t)=>e.then(()=>t.continue().catch(er)),Promise.resolve()))}).then(()=>{this.resuming=void 0}),this.resuming}}function Fr(){return{onFetch:e=>{e.fetchFn=()=>{var t,n,r,i,o,s;const a=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,c=null==(r=e.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,l=null==c?void 0:c.pageParam,u="forward"===(null==c?void 0:c.direction),d="backward"===(null==c?void 0:c.direction),h=(null==(o=e.state.data)?void 0:o.pages)||[],f=(null==(s=e.state.data)?void 0:s.pageParams)||[];let p=f,m=!1;const v=e.options.queryFn||(()=>Promise.reject("Missing queryFn for queryKey '"+e.options.queryHash+"'")),y=(e,t,n,r)=>(p=r?[t,...p]:[...p,t],r?[n,...e]:[...e,n]),g=(t,n,r,i)=>{if(m)return Promise.reject("Cancelled");if(void 0===r&&!n&&t.length)return Promise.resolve(t);const o={queryKey:e.queryKey,pageParam:r,meta:e.options.meta};var s;s=o,Object.defineProperty(s,"signal",{enumerable:!0,get:()=>{var t,n;return null!=(t=e.signal)&&t.aborted?m=!0:null==(n=e.signal)||n.addEventListener("abort",()=>{m=!0}),e.signal}});const a=v(o);return Promise.resolve(a).then(e=>y(t,r,e,i))};let b;if(h.length)if(u){const t=void 0!==l,n=t?l:Dr(e.options,h);b=g(h,t,n)}else if(d){const t=void 0!==l,n=t?l:(_=e.options,w=h,null==_.getPreviousPageParam?void 0:_.getPreviousPageParam(w[0],w));b=g(h,t,n,!0)}else{p=[];const t=void 0===e.options.getNextPageParam;b=!a||!h[0]||a(h[0],0,h)?g([],t,f[0]):Promise.resolve(y([],f[0],h[0]));for(let n=1;n<h.length;n++)b=b.then(r=>{if(!a||!h[n]||a(h[n],n,h)){const i=t?f[n]:Dr(e.options,r);return g(r,t,i)}return Promise.resolve(y(r,f[n],h[n]))})}else b=g([]);var _,w;const x=b.then(e=>({pages:e,pageParams:p}));return x}}}}function Dr(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}function Lr(e,t){return function(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&Ir(e,t,t.refetchOnMount)}function Ir(e,t,n){if(!1!==t.enabled){const r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&Ur(e,t)}return!1}function qr(e,t,n,r){return!1!==n.enabled&&(e!==t||!1===r.enabled)&&(!n.suspense||"error"!==e.state.status)&&Ur(e,n)}function Ur(e,t){return e.isStaleByTime(t.staleTime)}let zr=class extends Xn{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;const n=this.options;this.options=this.client.defaultMutationOptions(e),hr(n,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.currentMutation)||e.removeObserver(this))}onMutationUpdate(e){this.updateResult();const t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:void 0!==e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const e=this.currentMutation?this.currentMutation.state:{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0},t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){Ar.batch(()=>{var t,n,r,i;if(this.mutateOptions&&this.hasListeners())if(e.onSuccess)null==(t=(n=this.mutateOptions).onSuccess)||t.call(n,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(r=(i=this.mutateOptions).onSettled)||r.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(e.onError){var o,s,a,c;null==(o=(s=this.mutateOptions).onError)||o.call(s,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(a=(c=this.mutateOptions).onSettled)||a.call(c,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)})})}};function Br(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Hr=Symbol.for("preact-signals");function Wr(){if(Gr>1)Gr--;else{for(var e,t=!1;void 0!==Vr;){var n=Vr;for(Vr=void 0,Jr++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&ti(n))try{n.c()}catch(i){t||(e=i,t=!0)}n=r}}if(Jr=0,Gr--,t)throw e}}function $r(e){if(Gr>0)return e();Gr++;try{return e()}finally{Wr()}}var Qr=void 0;function Kr(e){var t=Qr;Qr=void 0;try{return e()}finally{Qr=t}}var Vr=void 0,Gr=0,Jr=0,Yr=0;function Xr(e){if(void 0!==Qr){var t=e.n;if(void 0===t||t.t!==Qr)return t={i:0,S:e,p:Qr.s,n:void 0,t:Qr,e:void 0,x:void 0,r:t},void 0!==Qr.s&&(Qr.s.n=t),Qr.s=t,e.n=t,32&Qr.f&&e.S(t),t;if(-1===t.i)return t.i=0,void 0!==t.n&&(t.n.p=t.p,void 0!==t.p&&(t.p.n=t.n),t.p=Qr.s,t.n=void 0,Qr.s.n=t,Qr.s=t),t}}function Zr(e,t){this.v=e,this.i=0,this.n=void 0,this.t=void 0,this.W=null==t?void 0:t.watched,this.Z=null==t?void 0:t.unwatched}function ei(e,t){return new Zr(e,t)}function ti(e){for(var t=e.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function ni(e){for(var t=e.s;void 0!==t;t=t.n){var n=t.S.n;if(void 0!==n&&(t.r=n),t.S.n=t,t.i=-1,void 0===t.n){e.s=t;break}}}function ri(e){for(var t=e.s,n=void 0;void 0!==t;){var r=t.p;-1===t.i?(t.S.U(t),void 0!==r&&(r.n=t.n),void 0!==t.n&&(t.n.p=r)):n=t,t.S.n=t.r,void 0!==t.r&&(t.r=void 0),t=r}e.s=n}function ii(e,t){Zr.call(this,void 0),this.x=e,this.s=void 0,this.g=Yr-1,this.f=4,this.W=null==t?void 0:t.watched,this.Z=null==t?void 0:t.unwatched}function oi(e,t){return new ii(e,t)}function si(e){var t=e.u;if(e.u=void 0,"function"==typeof t){Gr++;var n=Qr;Qr=void 0;try{t()}catch(r){throw e.f&=-2,e.f|=8,ai(e),r}finally{Qr=n,Wr()}}}function ai(e){for(var t=e.s;void 0!==t;t=t.n)t.S.U(t);e.x=void 0,e.s=void 0,si(e)}function ci(e){if(Qr!==this)throw new Error("Out-of-order effect");ri(this),Qr=e,this.f&=-2,8&this.f&&ai(this),Wr()}function li(e){this.x=e,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function ui(e){var t=new li(e);try{t.c()}catch(r){throw t.d(),r}var n=t.d.bind(t);return n[Symbol.dispose]=n,n}Zr.prototype.brand=Hr,Zr.prototype.h=function(){return!0},Zr.prototype.S=function(e){var t=this,n=this.t;n!==e&&void 0===e.e&&(e.x=n,this.t=e,void 0!==n?n.e=e:Kr(function(){var e;null==(e=t.W)||e.call(t)}))},Zr.prototype.U=function(e){var t=this;if(void 0!==this.t){var n=e.e,r=e.x;void 0!==n&&(n.x=r,e.e=void 0),void 0!==r&&(r.e=n,e.x=void 0),e===this.t&&(this.t=r,void 0===r&&Kr(function(){var e;null==(e=t.Z)||e.call(t)}))}},Zr.prototype.subscribe=function(e){var t=this;return ui(function(){var n=t.value,r=Qr;Qr=void 0;try{e(n)}finally{Qr=r}})},Zr.prototype.valueOf=function(){return this.value},Zr.prototype.toString=function(){return this.value+""},Zr.prototype.toJSON=function(){return this.value},Zr.prototype.peek=function(){var e=Qr;Qr=void 0;try{return this.value}finally{Qr=e}},Object.defineProperty(Zr.prototype,"value",{get:function(){var e=Xr(this);return void 0!==e&&(e.i=this.i),this.v},set:function(e){if(e!==this.v){if(Jr>100)throw new Error("Cycle detected");this.v=e,this.i++,Yr++,Gr++;try{for(var t=this.t;void 0!==t;t=t.x)t.t.N()}finally{Wr()}}}}),ii.prototype=new Zr,ii.prototype.h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===Yr)return!0;if(this.g=Yr,this.f|=1,this.i>0&&!ti(this))return this.f&=-2,!0;var e=Qr;try{ni(this),Qr=this;var t=this.x();(16&this.f||this.v!==t||0===this.i)&&(this.v=t,this.f&=-17,this.i++)}catch(n){this.v=n,this.f|=16,this.i++}return Qr=e,ri(this),this.f&=-2,!0},ii.prototype.S=function(e){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}Zr.prototype.S.call(this,e)},ii.prototype.U=function(e){if(void 0!==this.t&&(Zr.prototype.U.call(this,e),void 0===this.t)){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}},ii.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var e=this.t;void 0!==e;e=e.x)e.t.N()}},Object.defineProperty(ii.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var e=Xr(this);if(this.h(),void 0!==e&&(e.i=this.i),16&this.f)throw this.v;return this.v}}),li.prototype.c=function(){var e=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();"function"==typeof t&&(this.u=t)}finally{e()}},li.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,si(this),ni(this),Gr++;var e=Qr;return Qr=this,ci.bind(this,e)},li.prototype.N=function(){2&this.f||(this.f|=2,this.o=Vr,Vr=this)},li.prototype.d=function(){this.f|=8,1&this.f||ai(this)},li.prototype.dispose=function(){this.d()};var di,hi,fi,pi=[],mi=[];function vi(e,n){t[e]=n.bind(null,t[e]||function(){})}function yi(e){fi&&fi(),fi=e&&e.S()}function gi(e){var t=this,n=e.data,i=_i(n);i.value=n;var o=Wt(function(){for(var e=t,n=t.__v;n=n.__;)if(n.__c){n.__c.__$f|=4;break}var o=oi(function(){var e=i.value.value;return 0===e?0:!0===e?"":e||""}),s=oi(function(){return!Array.isArray(o.value)&&!r(o.value)}),a=ui(function(){if(this.N=Ci,s.value){var t=o.value;e.__v&&e.__v.__e&&3===e.__v.__e.nodeType&&(e.__v.__e.data=t)}}),c=t.__$u.d;return t.__$u.d=function(){a(),c.call(this)},[s,o]},[]),s=o[0],a=o[1];return s.value?a.peek():a.value}function bi(e,t,n,r){var i=t in e&&void 0===e.ownerSVGElement,o=ei(n);return{o:function(e,t){o.value=e,r=t},d:ui(function(){this.N=Ci;var n=o.value.value;r[t]!==n&&(r[t]=n,i?e[t]=n:n?e.setAttribute(t,n):e.removeAttribute(t))})}}function _i(e,t){return Wt(function(){return ei(e,t)},[])}function wi(e,t){var n=Bt(e);return n.current=e,hi.__$f|=4,Wt(function(){return oi(function(){return n.current()},t)},[])}ui(function(){di=this.N})(),gi.displayName="_st",Object.defineProperties(Zr.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:gi},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),vi("__b",function(e,t){if("string"==typeof t.type){var n,r=t.props;for(var i in r)if("children"!==i){var o=r[i];o instanceof Zr&&(n||(t.__np=n={}),n[i]=o,r[i]=o.peek())}}e(t)}),vi("__r",function(e,t){if(t.type!==w){yi();var n,r=t.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=(ui(function(){i=this}),i.c=function(){r.__$f|=1,r.setState({})},n=i))),hi=r,yi(n)}var i;e(t)}),vi("__e",function(e,t,n,r){yi(),hi=void 0,e(t,n,r)}),vi("diffed",function(e,t){var n;if(yi(),hi=void 0,"string"==typeof t.type&&(n=t.__e)){var r=t.__np,i=t.props;if(r){var o=n.U;if(o)for(var s in o){var a=o[s];void 0===a||s in r||(a.d(),o[s]=void 0)}else o={},n.U=o;for(var c in r){var l=o[c],u=r[c];void 0===l?(l=bi(n,c,u,i),o[c]=l):l.o(u,i)}}}e(t)}),vi("unmount",function(e,t){if("string"==typeof t.type){var n=t.__e;if(n){var r=n.U;if(r)for(var i in n.U=void 0,r){var o=r[i];o&&o.d()}}}else{var s=t.__c;if(s){var a=s.__$u;a&&(s.__$u=void 0,a.d())}}e(t)}),vi("__h",function(e,t,n,r){(r<3||9===r)&&(t.__$f|=2),e(t,n,r)}),x.prototype.shouldComponentUpdate=function(e,t){var n=this.__$u,r=n&&void 0!==n.s;for(var i in t)return!0;if(this.__f||"boolean"==typeof this.u&&!0===this.u){var o=2&this.__$f;if(!(r||o||4&this.__$f))return!0;if(1&this.__$f)return!0}else{if(!(r||4&this.__$f))return!0;if(3&this.__$f)return!0}for(var s in e)if("__source"!==s&&e[s]!==this.props[s])return!0;for(var a in this.props)if(!(a in e))return!0;return!1};var xi="undefined"==typeof requestAnimationFrame?setTimeout:function(e){var t=function(){clearTimeout(n),cancelAnimationFrame(r),e()},n=setTimeout(t,35),r=requestAnimationFrame(t)},ki=function(e){queueMicrotask(function(){queueMicrotask(e)})};function Si(){$r(function(){for(var e;e=pi.shift();)di.call(e)})}function Ei(){1===pi.push(this)&&(t.requestAnimationFrame||xi)(Si)}function Oi(){$r(function(){for(var e;e=mi.shift();)di.call(e)})}function Ci(){1===mi.push(this)&&(t.requestAnimationFrame||ki)(Oi)}function Ri(e){var t=Bt(e);t.current=e,Ut(function(){return ui(function(){return this.N=Ei,t.current()})},[])}const Ai=Object.freeze(Object.defineProperty({__proto__:null,Signal:Zr,batch:$r,computed:oi,effect:ui,signal:ei,untracked:Kr,useComputed:wi,useSignal:_i,useSignalEffect:Ri},Symbol.toStringTag,{value:"Module"}));var Ni,Pi,Ti;const Mi=()=>{throw new Error("preact signals runtime not implemented hooks")},{Signal:ji,batch:Fi,computed:Di,effect:Li,signal:Ii,untracked:qi}=Ai,Ui=null!=(Ni=null==Ai?void 0:wi)?Ni:Mi,zi=null!=(Pi=null==Ai?void 0:_i)?Pi:Mi,Bi=null!=(Ti=null==Ai?void 0:Ri)?Ti:Mi,Hi=e=>t=>{Fi(()=>{for(const n in t)e[n]=t[n]})},Wi=Symbol("store-state"),$i={get(e,t,n){if(t===Wi)return e[t];const r=e[Wi];if(t in r)return r[t]?.value;const i=Object.getOwnPropertyDescriptor(e,t);return i?.get?(r[t]=Di(i.get?.bind(n)),delete e[t],r[t]?.value):"function"==typeof e[t]?e[t]:(r[t]=Ii(e[t]),delete e[t],r[t].value)},set(e,t,n){if("function"==typeof e[t])return e[t]=n,!0;const r=e[Wi];return t in e&&delete e[t],r[t]?(r[t].value=n,!0):(r[t]=Ii(n),!0)},deleteProperty(e,t){const n=t in e?e:t in e[Wi]?e[Wi]:null;return!!n&&(delete n[t],!0)},has:(e,t)=>t in e||t in e[Wi],ownKeys:e=>[...Object.keys(e),...Object.keys(e[Wi])],getOwnPropertyDescriptor:()=>({enumerable:!0,configurable:!0})},Qi=e=>{const t=(e=>e[Wi]?e:(e[Wi]={},new Proxy(e,$i)))(e);return[t,Hi(t)]};var Ki,Vi;function Gi(e){this[Ki.Accessor]=e}function Ji(e,t){this[Ki.Accessor]=e,this[Ki.Setter]=t}(Vi=Ki||(Ki={})).Accessor="_a",Vi.Setter="_s",Gi.prototype=Object.create(ji.prototype),Ji.prototype=Object.create(Gi.prototype),Object.defineProperties(Gi.prototype,{value:{get(){return this[Ki.Accessor]()},set(){throw new Error("Uncached value is readonly")}},peek:{value(){return qi(()=>this[Ki.Accessor]())}},valueOf:{value(){return this[Ki.Accessor]()}},toString:{value(){return String(this[Ki.Accessor]())}}}),Object.defineProperty(Ji.prototype,"value",{get(){return this[Ki.Accessor]()},set(e){this[Ki.Setter](e)}});const Yi=e=>new Gi(e),Xi=[],Zi=e=>Ui(()=>{return"function"==typeof(t=e)?t():t.value;var t}),eo=e=>{const t=zi(e);return t.peek()!==e&&(t.value=e),t},to=e=>eo(Qt(e)),no=e=>{Ut(()=>Li(e),Xi)},ro=e=>{const t=Bt(null);return null===t.current&&(t.current=Di(e)),t.current},io=H(void 0),oo=H(!1);function so(e,t){return e||(t&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=io),window.ReactQueryClientContext):io)}const ao=e=>eo((({context:e}={})=>{const t=Qt(so(e,Qt(oo)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t})(e)),co=({client:e,children:t,context:n,contextSharing:r=!1})=>{Ut(()=>(e.mount(),()=>{e.unmount()}),[e]);const i=so(n,r);return b(oo.Provider,{value:!n&&r},b(i.Provider,{value:e},t))},lo=H(!1);lo.Provider;const uo=H(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}());function ho(e,t){return"function"==typeof e?e(...t):!!e}const fo=e=>{const t=Zi(e),[n,r]=(e=>{const t=Bt();return t.current||(t.current=Qi(qi(e))),t.current})(()=>t.value.getCurrent());return no(()=>{r(t.value.getCurrent())}),no(()=>t.value.subscribe(r)),n},po=[],mo={apply:(e,t,n)=>qi(()=>e.apply(t,n))},vo=e=>new Proxy(e,mo),yo=e=>{for(const t in e)"function"==typeof e[t]&&(e[t]=vo(e[t]));return e},go=e=>{const t=Bt(e);t.current=e;const n=$t(()=>t.current(),[e]),r=Bt(!0),i=zi(n);r.current&&(i.value=n);const o=Ui(()=>i.value());return Bi(()=>{r.current=o.value.executeOptionsOnReferenceChange??true}),o};var bo,_o;(_o=bo||(bo={}))[_o.Error=0]="Error",_o[_o.Success=1]="Success",_o[_o.Suspense=2]="Suspense";const wo=(xo=class extends Xn{constructor(e,t){super(),this.client=e,this.options=t,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(t)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.currentQuery.addObserver(this),Lr(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Ir(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Ir(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(e,t){const n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryOptions(e),hr(n,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();const i=this.hasListeners();i&&qr(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(t),!i||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();const o=this.computeRefetchInterval();!i||this.currentQuery===r&&this.options.enabled===n.enabled&&o===this.currentRefetchInterval||this.updateRefetchInterval(o)}getOptimisticResult(e){const t=this.client.getQueryCache().build(this.client,e),n=this.createResult(t,e);return function(e,t,n){return!n.keepPreviousData&&(void 0!==n.placeholderData?t.isPlaceholderData:e.getCurrentResult()!==t)}(this,n,e)&&(this.currentResult=n,this.currentResultOptions=this.options,this.currentResultState=this.currentQuery.state),n}getCurrentResult(){return this.currentResult}trackResult(e){const t={};return Object.keys(e).forEach(n=>{Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(n),e[n])})}),t}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch({refetchPage:e,...t}={}){return this.fetch({...t,meta:{refetchPage:e}})}fetchOptimistic(e){const t=this.client.defaultQueryOptions(e),n=this.client.getQueryCache().build(this.client,t);return n.isFetchingOptimistic=!0,n.fetch().then(()=>this.createResult(n,t))}fetch(e){var t;return this.executeFetch({...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),this.currentResult))}executeFetch(e){this.updateQuery();let t=this.currentQuery.fetch(this.options,e);return null!=e&&e.throwOnError||(t=t.catch(er)),t}updateStaleTimeout(){if(this.clearStaleTimeout(),Zn||this.currentResult.isStale||!tr(this.options.staleTime))return;const e=nr(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(()=>{this.currentResult.isStale||this.updateResult()},e)}computeRefetchInterval(){var e;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e}updateRefetchInterval(e){this.clearRefetchInterval(),this.currentRefetchInterval=e,!Zn&&!1!==this.options.enabled&&tr(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(()=>{(this.options.refetchIntervalInBackground||_r.isFocused())&&this.executeFetch()},this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(e,t){const n=this.currentQuery,r=this.options,i=this.currentResult,o=this.currentResultState,s=this.currentResultOptions,a=e!==n,c=a?e.state:this.currentQueryInitialState,l=a?this.currentResult:this.previousQueryResult,{state:u}=e;let d,{dataUpdatedAt:h,error:f,errorUpdatedAt:p,fetchStatus:m,status:v}=u,y=!1,g=!1;if(t._optimisticResults){const i=this.hasListeners(),o=!i&&Lr(e,t),s=i&&qr(e,n,t,r);(o||s)&&(m=Sr(e.options.networkMode)?"fetching":"paused",h||(v="loading")),"isRestoring"===t._optimisticResults&&(m="idle")}if(t.keepPreviousData&&!u.dataUpdatedAt&&null!=l&&l.isSuccess&&"error"!==v)d=l.data,h=l.dataUpdatedAt,v=l.status,y=!0;else if(t.select&&void 0!==u.data)if(i&&u.data===(null==o?void 0:o.data)&&t.select===this.selectFn)d=this.selectResult;else try{this.selectFn=t.select,d=t.select(u.data),d=br(null==i?void 0:i.data,d,t),this.selectResult=d,this.selectError=null}catch(x){this.selectError=x}else d=u.data;if(void 0!==t.placeholderData&&void 0===d&&"loading"===v){let e;if(null!=i&&i.isPlaceholderData&&t.placeholderData===(null==s?void 0:s.placeholderData))e=i.data;else if(e="function"==typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.selectError=null}catch(x){this.selectError=x}void 0!==e&&(v="success",d=br(null==i?void 0:i.data,e,t),g=!0)}this.selectError&&(f=this.selectError,d=this.selectResult,p=Date.now(),v="error");const b="fetching"===m,_="loading"===v,w="error"===v;return{status:v,fetchStatus:m,isLoading:_,isSuccess:"success"===v,isError:w,isInitialLoading:_&&b,data:d,dataUpdatedAt:h,error:f,errorUpdatedAt:p,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>c.dataUpdateCount||u.errorUpdateCount>c.errorUpdateCount,isFetching:b,isRefetching:b&&!_,isLoadingError:w&&0===u.dataUpdatedAt,isPaused:"paused"===m,isPlaceholderData:g,isPreviousData:y,isRefetchError:w&&0!==u.dataUpdatedAt,isStale:Ur(e,t),refetch:this.refetch,remove:this.remove}}updateResult(e){const t=this.currentResult,n=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,hr(n,t))return;this.currentResult=n;const r={cache:!0};!1!==(null==e?void 0:e.listeners)&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options;if("all"===e||!e&&!this.trackedProps.size)return!0;const n=new Set(null!=e?e:this.trackedProps);return this.options.useErrorBoundary&&n.add("error"),Object.keys(this.currentResult).some(e=>{const r=e;return this.currentResult[r]!==t[r]&&n.has(r)})})()&&(r.listeners=!0),this.notify({...r,...e})}updateQuery(){const e=this.client.getQueryCache().build(this.client,this.options);if(e===this.currentQuery)return;const t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}onQueryUpdate(e){const t={};"success"===e.type?t.onSuccess=!e.manual:"error"!==e.type||Or(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()}notify(e){Ar.batch(()=>{var t,n,r,i;if(e.onSuccess)null==(t=(n=this.options).onSuccess)||t.call(n,this.currentResult.data),null==(r=(i=this.options).onSettled)||r.call(i,this.currentResult.data,null);else if(e.onError){var o,s,a,c;null==(o=(s=this.options).onError)||o.call(s,this.currentResult.error),null==(a=(c=this.options).onSettled)||a.call(c,void 0,this.currentResult.error)}e.listeners&&this.listeners.forEach(({listener:e})=>{e(this.currentResult)}),e.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})})}},e=>{const t=go(e),n=ao({context:ro(()=>t.value.context).value}),r=to(lo),i=to(uo),o=Yi(()=>t.value.suspenseBehavior??"load-on-access"),s=ro(()=>{const e=yo(n.value.defaultQueryOptions(t.value));var o;return e._optimisticResults=r.value?"isRestoring":"optimistic",(o=e).suspense&&"number"!=typeof o.staleTime&&(o.staleTime=1e3),((e,t)=>{(e.suspense||e.useErrorBoundary)&&(t.isReset()||(e.retryOnMount=!1))})(e,i.value),e}),a=ro(()=>new xo(n.value,s.peek()));no(()=>{a.value.setOptions(s.value)});const c=fo(()=>({getCurrent:()=>a.value.getOptimisticResult(s.value),subscribe:e=>a.value.subscribe(t=>{e(t)})}));(e=>{const t=Zi(e);no(()=>{t.value.clearReset()})})(i);const l=Yi(()=>{return e=s.value,t=c,n=r.value,e?.suspense&&((e,t)=>e.isLoading&&e.isFetching&&!t)(t,n);var e,t,n}),u=()=>(({result:e,errorResetBoundary:t,useErrorBoundary:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&ho(n,[e.error,r]))({result:c,errorResetBoundary:i.value,query:a.value.getCurrentQuery(),useErrorBoundary:s.value.useErrorBoundary})?{type:bo.Error,data:c.error}:l.value?{type:bo.Suspense,data:a.value.fetchOptimistic(s.value)}:{type:bo.Success,data:c.data},d=ro(()=>{const e=u();if(e.type===bo.Success)return e.data;throw e.data});return qi(()=>{if(l.value&&"load-on-access"!==o.value){const e=u();if(e.type===bo.Suspense&&"suspend-eagerly"===o.value)throw e.data}}),ro(()=>!(!l.value||"suspend-eagerly"!==o.value)&&u().type!==bo.Success).value,c.dataSafe=void 0,Wt(()=>new Proxy(c,{get(e,t){return"data"===t?d.value:"dataSafe"===t?e.data:Reflect.get(...arguments)}}),[])});var xo;function ko(){}const So=e=>{const t=go(e),n=ao({context:ro(()=>t.value.context).value}),r=ro(()=>new zr(n.value,yo(t.peek())));Bi(()=>{r.value.setOptions(yo(t.value))});const i=Wt(()=>(e,t)=>{r.peek().mutate(e,t).catch(ko)},po),o=e=>({...e,mutate:i,mutateAsync:e.mutate}),s=fo(()=>({getCurrent:()=>o(r.value.getCurrentResult()),subscribe:e=>r.value.subscribe(t=>{e(o(t))})}));return ro(()=>s.error&&ho(r.value.options.useErrorBoundary,[s.error])).value&&qi(()=>{throw s.error}),s},Eo=()=>{const[e,t]=It(window.EurolandAppContext?.command("authState")||{});return Ut(()=>{const e=()=>{t(window.EurolandAppContext?.command("authState"))};return window.EurolandAppContext?.on("authChanged",e),()=>{window.EurolandAppContext?.off("authChanged",e)}},[]),e},Oo=window.self!==window.top,Co=({instrumentId:e})=>Eo().isAuthenticated?$("button",{className:"add-instrument-button",onClick:()=>{const t=window.euroland?.components.WatchlistAddInstrument({instrumentId:e});if(!t)return void console.error("euroland component not available");const n=window.xprops?.layout?.middle||"#middleLayout";if(Oo)t.renderTo(window.parent,n);else{let e=document.getElementById("middleLayout");e||(e=document.createElement("div"),e.id="middleLayout",document.body.appendChild(e)),t.renderTo(window.parent,n)}},children:"Add to watchlist"}):null;function Ro(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ao}=Object.prototype,{getPrototypeOf:No}=Object,{iterator:Po,toStringTag:To}=Symbol,Mo=(e=>t=>{const n=Ao.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),jo=e=>(e=e.toLowerCase(),t=>Mo(t)===e),Fo=e=>t=>typeof t===e,{isArray:Do}=Array,Lo=Fo("undefined");const Io=jo("ArrayBuffer");const qo=Fo("string"),Uo=Fo("function"),zo=Fo("number"),Bo=e=>null!==e&&"object"==typeof e,Ho=e=>{if("object"!==Mo(e))return!1;const t=No(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||To in e||Po in e)},Wo=jo("Date"),$o=jo("File"),Qo=jo("Blob"),Ko=jo("FileList"),Vo=jo("URLSearchParams"),[Go,Jo,Yo,Xo]=["ReadableStream","Request","Response","Headers"].map(jo);function Zo(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,i;if("object"!=typeof e&&(e=[e]),Do(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let s;for(r=0;r<o;r++)s=i[r],t.call(null,e[s],s,e)}}function es(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const ts="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ns=e=>!Lo(e)&&e!==ts;const rs=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&No(Uint8Array)),is=jo("HTMLFormElement"),os=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ss=jo("RegExp"),as=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Zo(n,(n,i)=>{let o;!1!==(o=t(n,i,e))&&(r[i]=o||n)}),Object.defineProperties(e,r)};const cs=jo("AsyncFunction"),ls=(us="function"==typeof setImmediate,ds=Uo(ts.postMessage),us?setImmediate:ds?(hs=`axios@${Math.random()}`,fs=[],ts.addEventListener("message",({source:e,data:t})=>{e===ts&&t===hs&&fs.length&&fs.shift()()},!1),e=>{fs.push(e),ts.postMessage(hs,"*")}):e=>setTimeout(e));var us,ds,hs,fs;const ps="undefined"!=typeof queueMicrotask?queueMicrotask.bind(ts):"undefined"!=typeof process&&process.nextTick||ls,ms={isArray:Do,isArrayBuffer:Io,isBuffer:function(e){return null!==e&&!Lo(e)&&null!==e.constructor&&!Lo(e.constructor)&&Uo(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||Uo(e.append)&&("formdata"===(t=Mo(e))||"object"===t&&Uo(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Io(e.buffer),t},isString:qo,isNumber:zo,isBoolean:e=>!0===e||!1===e,isObject:Bo,isPlainObject:Ho,isReadableStream:Go,isRequest:Jo,isResponse:Yo,isHeaders:Xo,isUndefined:Lo,isDate:Wo,isFile:$o,isBlob:Qo,isRegExp:ss,isFunction:Uo,isStream:e=>Bo(e)&&Uo(e.pipe),isURLSearchParams:Vo,isTypedArray:rs,isFileList:Ko,forEach:Zo,merge:function e(){const{caseless:t}=ns(this)&&this||{},n={},r=(r,i)=>{const o=t&&es(n,i)||i;Ho(n[o])&&Ho(r)?n[o]=e(n[o],r):Ho(r)?n[o]=e({},r):Do(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&Zo(arguments[i],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(Zo(t,(t,r)=>{n&&Uo(t)?e[r]=Ro(t,n):e[r]=t},{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,o,s;const a={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],r&&!r(s,e,t)||a[s]||(t[s]=e[s],a[s]=!0);e=!1!==n&&No(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Mo,kindOfTest:jo,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Do(e))return e;let t=e.length;if(!zo(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Po]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:is,hasOwnProperty:os,hasOwnProp:os,reduceDescriptors:as,freezeMethods:e=>{as(e,(t,n)=>{if(Uo(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Uo(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Do(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:es,global:ts,isContextDefined:ns,isSpecCompliantForm:function(e){return!!(e&&Uo(e.append)&&"FormData"===e[To]&&e[Po])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Bo(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const i=Do(e)?[]:{};return Zo(e,(e,t)=>{const o=n(e,r+1);!Lo(o)&&(i[t]=o)}),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:cs,isThenable:e=>e&&(Bo(e)||Uo(e))&&Uo(e.then)&&Uo(e.catch),setImmediate:ls,asap:ps,isIterable:e=>null!=e&&Uo(e[Po])};function vs(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}ms.inherits(vs,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ms.toJSONObject(this.config),code:this.code,status:this.status}}});const ys=vs.prototype,gs={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{gs[e]={value:e}}),Object.defineProperties(vs,gs),Object.defineProperty(ys,"isAxiosError",{value:!0}),vs.from=(e,t,n,r,i,o)=>{const s=Object.create(ys);return ms.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),vs.call(s,e.message,t,n,r,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};function bs(e){return ms.isPlainObject(e)||ms.isArray(e)}function _s(e){return ms.endsWith(e,"[]")?e.slice(0,-2):e}function ws(e,t,n){return e?e.concat(t).map(function(e,t){return e=_s(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const xs=ms.toFlatObject(ms,{},null,function(e){return/^is[A-Z]/.test(e)});function ks(e,t,n){if(!ms.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ms.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!ms.isUndefined(t[e])})).metaTokens,i=n.visitor||l,o=n.dots,s=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ms.isSpecCompliantForm(t);if(!ms.isFunction(i))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ms.isDate(e))return e.toISOString();if(ms.isBoolean(e))return e.toString();if(!a&&ms.isBlob(e))throw new vs("Blob is not supported. Use a Buffer instead.");return ms.isArrayBuffer(e)||ms.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,i){let a=e;if(e&&!i&&"object"==typeof e)if(ms.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ms.isArray(e)&&function(e){return ms.isArray(e)&&!e.some(bs)}(e)||(ms.isFileList(e)||ms.endsWith(n,"[]"))&&(a=ms.toArray(e)))return n=_s(n),a.forEach(function(e,r){!ms.isUndefined(e)&&null!==e&&t.append(!0===s?ws([n],r,o):null===s?n:n+"[]",c(e))}),!1;return!!bs(e)||(t.append(ws(i,n,o),c(e)),!1)}const u=[],d=Object.assign(xs,{defaultVisitor:l,convertValue:c,isVisitable:bs});if(!ms.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ms.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ms.forEach(n,function(n,o){!0===(!(ms.isUndefined(n)||null===n)&&i.call(t,n,ms.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])}),u.pop()}}(e),t}function Ss(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function Es(e,t){this._pairs=[],e&&ks(e,this,t)}const Os=Es.prototype;function Cs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Rs(e,t,n){if(!t)return e;const r=n&&n.encode||Cs;ms.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(o=i?i(t,n):ms.isURLSearchParams(t)?t.toString():new Es(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}Os.append=function(e,t){this._pairs.push([e,t])},Os.toString=function(e){const t=e?function(t){return e.call(this,t,Ss)}:Ss;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class As{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ms.forEach(this.handlers,function(t){null!==t&&e(t)})}}const Ns={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ps={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Es,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Ts="undefined"!=typeof window&&"undefined"!=typeof document,Ms="object"==typeof navigator&&navigator||void 0,js=Ts&&(!Ms||["ReactNative","NativeScript","NS"].indexOf(Ms.product)<0),Fs="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Ds=Ts&&window.location.href||"http://localhost",Ls={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ts,hasStandardBrowserEnv:js,hasStandardBrowserWebWorkerEnv:Fs,navigator:Ms,origin:Ds},Symbol.toStringTag,{value:"Module"})),...Ps};function Is(e){function t(e,n,r,i){let o=e[i++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),a=i>=e.length;if(o=!o&&ms.isArray(r)?r.length:o,a)return ms.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!s;r[o]&&ms.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],i)&&ms.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}(r[o])),!s}if(ms.isFormData(e)&&ms.isFunction(e.entries)){const n={};return ms.forEachEntry(e,(e,r)=>{t(function(e){return ms.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null}const qs={transitional:Ns,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=ms.isObject(e);i&&ms.isHTMLForm(e)&&(e=new FormData(e));if(ms.isFormData(e))return r?JSON.stringify(Is(e)):e;if(ms.isArrayBuffer(e)||ms.isBuffer(e)||ms.isStream(e)||ms.isFile(e)||ms.isBlob(e)||ms.isReadableStream(e))return e;if(ms.isArrayBufferView(e))return e.buffer;if(ms.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ks(e,new Ls.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Ls.isNode&&ms.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=ms.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ks(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(ms.isString(e))try{return(t||JSON.parse)(e),ms.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||qs.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ms.isResponse(e)||ms.isReadableStream(e))return e;if(e&&ms.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(i){if(n){if("SyntaxError"===i.name)throw vs.from(i,vs.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ls.classes.FormData,Blob:Ls.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ms.forEach(["delete","get","head","post","put","patch"],e=>{qs.headers[e]={}});const Us=ms.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),zs=Symbol("internals");function Bs(e){return e&&String(e).trim().toLowerCase()}function Hs(e){return!1===e||null==e?e:ms.isArray(e)?e.map(Hs):String(e)}function Ws(e,t,n,r,i){return ms.isFunction(r)?r.call(this,t,n):(i&&(t=n),ms.isString(t)?ms.isString(r)?-1!==t.indexOf(r):ms.isRegExp(r)?r.test(t):void 0:void 0)}let $s=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=Bs(t);if(!i)throw new Error("header name must be a non-empty string");const o=ms.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=Hs(e))}const o=(e,t)=>ms.forEach(e,(e,n)=>i(e,n,t));if(ms.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(ms.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach(function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&Us[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(ms.isObject(e)&&ms.isIterable(e)){let n,r,i={};for(const t of e){if(!ms.isArray(t))throw TypeError("Object iterator must return a key-value pair");i[r=t[0]]=(n=i[r])?ms.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(i,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=Bs(e)){const n=ms.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ms.isFunction(t))return t.call(this,e,n);if(ms.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Bs(e)){const n=ms.findKey(this,e);return!(!n||void 0===this[n]||t&&!Ws(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=Bs(e)){const i=ms.findKey(n,e);!i||t&&!Ws(0,n[i],i,t)||(delete n[i],r=!0)}}return ms.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!Ws(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return ms.forEach(this,(r,i)=>{const o=ms.findKey(n,i);if(o)return t[o]=Hs(r),void delete t[i];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(i):String(i).trim();s!==i&&delete t[i],t[s]=Hs(r),n[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return ms.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ms.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){const t=(this[zs]=this[zs]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Bs(e);t[r]||(!function(e,t){const n=ms.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})})}(n,e),t[r]=!0)}return ms.isArray(e)?e.forEach(r):r(e),this}};function Qs(e,t){const n=this||qs,r=t||n,i=$s.from(r.headers);let o=r.data;return ms.forEach(e,function(e){o=e.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function Ks(e){return!(!e||!e.__CANCEL__)}function Vs(e,t,n){vs.call(this,null==e?"canceled":e,vs.ERR_CANCELED,t,n),this.name="CanceledError"}function Gs(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new vs("Request failed with status code "+n.status,[vs.ERR_BAD_REQUEST,vs.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}$s.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ms.reduceDescriptors($s.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),ms.freezeMethods($s),ms.inherits(Vs,vs,{__CANCEL__:!0});const Js=(e,t,n=3)=>{let r=0;const i=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,o=0,s=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[s];i||(i=c),n[o]=a,r[o]=c;let u=s,d=0;for(;u!==o;)d+=n[u++],u%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const h=l&&c-l;return h?Math.round(1e3*d/h):void 0}}(50,250);return function(e,t){let n,r,i=0,o=1e3/t;const s=(t,o=Date.now())=>{i=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-i;a>=o?s(e,t):(n=e,r||(r=setTimeout(()=>{r=null,s(n)},o-a)))},()=>n&&s(n)]}(n=>{const o=n.loaded,s=n.lengthComputable?n.total:void 0,a=o-r,c=i(a);r=o;e({loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})},n)},Ys=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Xs=e=>(...t)=>ms.asap(()=>e(...t)),Zs=Ls.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ls.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ls.origin),Ls.navigator&&/(msie|trident)/i.test(Ls.navigator.userAgent)):()=>!0,ea=Ls.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const s=[e+"="+encodeURIComponent(t)];ms.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),ms.isString(r)&&s.push("path="+r),ms.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function ta(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const na=e=>e instanceof $s?{...e}:e;function ra(e,t){t=t||{};const n={};function r(e,t,n,r){return ms.isPlainObject(e)&&ms.isPlainObject(t)?ms.merge.call({caseless:r},e,t):ms.isPlainObject(t)?ms.merge({},t):ms.isArray(t)?t.slice():t}function i(e,t,n,i){return ms.isUndefined(t)?ms.isUndefined(e)?void 0:r(void 0,e,0,i):r(e,t,0,i)}function o(e,t){if(!ms.isUndefined(t))return r(void 0,t)}function s(e,t){return ms.isUndefined(t)?ms.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,i,o){return o in t?r(n,i):o in e?r(void 0,n):void 0}const c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,n)=>i(na(e),na(t),0,!0)};return ms.forEach(Object.keys(Object.assign({},e,t)),function(r){const o=c[r]||i,s=o(e[r],t[r],r);ms.isUndefined(s)&&o!==a||(n[r]=s)}),n}const ia=e=>{const t=ra({},e);let n,{data:r,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:c}=t;if(t.headers=a=$s.from(a),t.url=Rs(ta(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ms.isFormData(r))if(Ls.hasStandardBrowserEnv||Ls.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(Ls.hasStandardBrowserEnv&&(i&&ms.isFunction(i)&&(i=i(t)),i||!1!==i&&Zs(t.url))){const e=o&&s&&ea.read(s);e&&a.set(o,e)}return t},oa="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=ia(e);let i=r.data;const o=$s.from(r.headers).normalize();let s,a,c,l,u,{responseType:d,onUploadProgress:h,onDownloadProgress:f}=r;function p(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function v(){if(!m)return;const r=$s.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Gs(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=v:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(v)},m.onabort=function(){m&&(n(new vs("Request aborted",vs.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new vs("Network Error",vs.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||Ns;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new vs(t,i.clarifyTimeoutError?vs.ETIMEDOUT:vs.ECONNABORTED,e,m)),m=null},void 0===i&&o.setContentType(null),"setRequestHeader"in m&&ms.forEach(o.toJSON(),function(e,t){m.setRequestHeader(t,e)}),ms.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),f&&([c,u]=Js(f,!0),m.addEventListener("progress",c)),h&&m.upload&&([a,l]=Js(h),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(s=t=>{m&&(n(!t||t.type?new Vs(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Ls.protocols.indexOf(y)?n(new vs("Unsupported protocol "+y+":",vs.ERR_BAD_REQUEST,e)):m.send(i||null)})},sa=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof vs?t:new Vs(t instanceof Error?t.message:t))}};let o=t&&setTimeout(()=>{o=null,i(new vs(`timeout ${t} of ms exceeded`,vs.ETIMEDOUT))},t);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));const{signal:a}=r;return a.unsubscribe=()=>ms.asap(s),a}},aa=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},ca=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},la=(e,t,n,r)=>{const i=async function*(e,t){for await(const n of ca(e))yield*aa(n,t)}(e,t);let o,s=0,a=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return a(),void e.close();let o=r.byteLength;if(n){let e=s+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},ua="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,da=ua&&"function"==typeof ReadableStream,ha=ua&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),fa=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},pa=da&&fa(()=>{let e=!1;const t=new Request(Ls.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ma=da&&fa(()=>ms.isReadableStream(new Response("").body)),va={stream:ma&&(e=>e.body)};var ya;ua&&(ya=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!va[e]&&(va[e]=ms.isFunction(ya[e])?t=>t[e]():(t,n)=>{throw new vs(`Response type '${e}' is not supported`,vs.ERR_NOT_SUPPORT,n)})}));const ga=async(e,t)=>{const n=ms.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(ms.isBlob(e))return e.size;if(ms.isSpecCompliantForm(e)){const t=new Request(Ls.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return ms.isArrayBufferView(e)||ms.isArrayBuffer(e)?e.byteLength:(ms.isURLSearchParams(e)&&(e+=""),ms.isString(e)?(await ha(e)).byteLength:void 0)})(t):n},ba={http:null,xhr:oa,fetch:ua&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:o,timeout:s,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:h}=ia(e);l=l?(l+"").toLowerCase():"text";let f,p=sa([i,o&&o.toAbortSignal()],s);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let v;try{if(c&&pa&&"get"!==n&&"head"!==n&&0!==(v=await ga(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(ms.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Ys(v,Js(Xs(c)));r=la(n.body,65536,e,t)}}ms.isString(d)||(d=d?"include":"omit");const i="credentials"in Request.prototype;f=new Request(t,{...h,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:i?d:void 0});let o=await fetch(f,h);const s=ma&&("stream"===l||"response"===l);if(ma&&(a||s&&m)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=o[t]});const t=ms.toFiniteNumber(o.headers.get("content-length")),[n,r]=a&&Ys(t,Js(Xs(a),!0))||[];o=new Response(la(o.body,65536,n,()=>{r&&r(),m&&m()}),e)}l=l||"text";let y=await va[ms.findKey(va,l)||"text"](o,e);return!s&&m&&m(),await new Promise((t,n)=>{Gs(t,n,{data:y,headers:$s.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:f})})}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new vs("Network Error",vs.ERR_NETWORK,e,f),{cause:y.cause||y});throw vs.from(y,y&&y.code,e,f)}})};ms.forEach(ba,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const _a=e=>`- ${e}`,wa=e=>ms.isFunction(e)||null===e||!1===e,xa=e=>{e=ms.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!wa(n)&&(r=ba[(t=String(n)).toLowerCase()],void 0===r))throw new vs(`Unknown adapter '${t}'`);if(r)break;i[t||"#"+o]=r}if(!r){const e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new vs("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(_a).join("\n"):" "+_a(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function ka(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Vs(null,e)}function Sa(e){ka(e),e.headers=$s.from(e.headers),e.data=Qs.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return xa(e.adapter||qs.adapter)(e).then(function(t){return ka(e),t.data=Qs.call(e,e.transformResponse,t),t.headers=$s.from(t.headers),t},function(t){return Ks(t)||(ka(e),t&&t.response&&(t.response.data=Qs.call(e,e.transformResponse,t.response),t.response.headers=$s.from(t.response.headers))),Promise.reject(t)})}const Ea="1.10.0",Oa={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Oa[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Ca={};Oa.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Ea+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,o)=>{if(!1===e)throw new vs(r(i," has been removed"+(t?" in "+t:"")),vs.ERR_DEPRECATED);return t&&!Ca[i]&&(Ca[i]=!0,console.warn(r(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,o)}},Oa.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const Ra={assertOptions:function(e,t,n){if("object"!=typeof e)throw new vs("options must be an object",vs.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],s=t[o];if(s){const t=e[o],n=void 0===t||s(t,o,e);if(!0!==n)throw new vs("option "+o+" must be "+n,vs.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new vs("Unknown option "+o,vs.ERR_BAD_OPTION)}},validators:Oa},Aa=Ra.validators;let Na=class{constructor(e){this.defaults=e||{},this.interceptors={request:new As,response:new As}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=ra(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&Ra.assertOptions(n,{silentJSONParsing:Aa.transitional(Aa.boolean),forcedJSONParsing:Aa.transitional(Aa.boolean),clarifyTimeoutError:Aa.transitional(Aa.boolean)},!1),null!=r&&(ms.isFunction(r)?t.paramsSerializer={serialize:r}:Ra.assertOptions(r,{encode:Aa.function,serialize:Aa.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Ra.assertOptions(t,{baseUrl:Aa.spelling("baseURL"),withXsrfToken:Aa.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&ms.merge(i.common,i[t.method]);i&&ms.forEach(["delete","get","head","post","put","patch","common"],e=>{delete i[e]}),t.headers=$s.concat(o,i);const s=[];let a=!0;this.interceptors.request.forEach(function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))});const c=[];let l;this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let u,d=0;if(!a){const e=[Sa.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=s.length;let h=t;for(d=0;d<u;){const e=s[d++],t=s[d++];try{h=e(h)}catch(f){t.call(this,f);break}}try{l=Sa.call(this,h)}catch(f){return Promise.reject(f)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return Rs(ta((e=ra(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};ms.forEach(["delete","get","head","options"],function(e){Na.prototype[e]=function(t,n){return this.request(ra(n||{},{method:e,url:t,data:(n||{}).data}))}}),ms.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,i){return this.request(ra(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Na.prototype[e]=t(),Na.prototype[e+"Form"]=t(!0)});const Pa={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Pa).forEach(([e,t])=>{Pa[t]=e});const Ta=function e(t){const n=new Na(t),r=Ro(Na.prototype.request,n);return ms.extend(r,Na.prototype,n,{allOwnKeys:!0}),ms.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(ra(t,n))},r}(qs);Ta.Axios=Na,Ta.CanceledError=Vs,Ta.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,i){n.reason||(n.reason=new Vs(e,r,i),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e(function(e){t=e}),cancel:t}}},Ta.isCancel=Ks,Ta.VERSION=Ea,Ta.toFormData=ks,Ta.AxiosError=vs,Ta.Cancel=Ta.CanceledError,Ta.all=function(e){return Promise.all(e)},Ta.spread=function(e){return function(t){return e.apply(null,t)}},Ta.isAxiosError=function(e){return ms.isObject(e)&&!0===e.isAxiosError},Ta.mergeConfig=ra,Ta.AxiosHeaders=$s,Ta.formToJSON=e=>Is(ms.isHTMLForm(e)?new FormData(e):e),Ta.getAdapter=xa,Ta.HttpStatusCode=Pa,Ta.default=Ta;const{Axios:Ma,AxiosError:ja,CanceledError:Fa,isCancel:Da,CancelToken:La,VERSION:Ia,all:qa,Cancel:Ua,isAxiosError:za,spread:Ba,toFormData:Ha,AxiosHeaders:Wa,HttpStatusCode:$a,formToJSON:Qa,getAdapter:Ka,mergeConfig:Va}=Ta;function Ga(){return window.EurolandAppContext.command("authState").accessToken}const Ja=new class{baseUrl="https://dev.vn.euroland.com/tools/widget.watchlist.api";getHeaders(){return{Authorization:`Bearer ${Ga()}`,"Content-Type":"application/json"}}handleError(e,t){if(Ta.isAxiosError(e)){const n=e.response?.data?.message||`${t}: ${e.message}`;throw new Error(n)}throw e}async fetchWatchlists(){try{return(await Ta.get(`${this.baseUrl}/find-all`,{headers:this.getHeaders()})).data}catch(e){this.handleError(e,"Failed to fetch watchlists")}}async addInstrument(e,t){try{return(await Ta.post(`${this.baseUrl}/add-detail`,{instrumentIds:[e],watchlistId:t},{headers:this.getHeaders()})).data}catch(n){this.handleError(n,"Failed to add instrument")}}async removeInstrument(e,t){try{await Ta.delete(`${this.baseUrl}/remove-detail`,{params:{WatchListId:e,Id:t.toString()},headers:{Authorization:`Bearer ${Ga()}`}})}catch(n){this.handleError(n,"Failed to remove instrument")}}async createWatchlist(e){try{return(await Ta.post(`${this.baseUrl}/add`,{name:e},{headers:this.getHeaders()})).data}catch(t){this.handleError(t,"Failed to create watchlist")}}async updateWatchlist(e,t){try{return(await Ta.put(`${this.baseUrl}/update`,{name:t,id:e},{headers:this.getHeaders()})).data}catch(n){this.handleError(n,"Failed to update watchlist")}}async deleteWatchlist(e){try{await Ta.delete(`${this.baseUrl}/delete/${e}`,{headers:{Authorization:`Bearer ${Ga()}`}})}catch(t){this.handleError(t,"Failed to delete watchlist")}}async getWatchlistById(e){try{return(await Ta.get(`${this.baseUrl}/find/${e}`,{headers:this.getHeaders()})).data}catch(t){this.handleError(t,"Failed to get watchlist by id")}}},Ya=()=>{const e=ao();return{watchlistsQuery:wo(()=>({queryKey:["watchlists"],queryFn:()=>Ja.fetchWatchlists(),staleTime:0})),createWatchlistMutation:So(()=>({mutationFn:e=>Ja.createWatchlist(e),onSuccess:()=>{e.value.invalidateQueries({queryKey:["watchlists"]})},onLoading:()=>{e.value.invalidateQueries({queryKey:["watchlists"]})}})),updateWatchlistMutation:So(()=>({mutationFn:({id:e,name:t})=>Ja.updateWatchlist(e,t),onSuccess:()=>{e.value.invalidateQueries({queryKey:["watchlists"]})}})),deleteWatchlistMutation:So(()=>({mutationFn:e=>Ja.deleteWatchlist(e),onSuccess:()=>{e.value.invalidateQueries({queryKey:["watchlists"]}),e.value.invalidateQueries({queryKey:["instruments"]})}})),addInstrumentMutation:So(()=>({mutationFn:({instrumentId:e,watchlistId:t})=>Ja.addInstrument(e,t),onSuccess:()=>{e.value.invalidateQueries({queryKey:["instruments"]}),e.value.invalidateQueries({queryKey:["watchlists"]})}})),removeInstrumentMutation:So(()=>({mutationFn:({watchlistId:e,instrumentId:t})=>Ja.removeInstrument(e,t),onSuccess:()=>{e.value.invalidateQueries({queryKey:["instruments"]}),e.value.invalidateQueries({queryKey:["watchlists"]})}}))}},Xa=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Za=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},ec=(...e)=>e.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */
var tc={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"};
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */const nc=({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,children:i,iconNode:o,class:s="",...a})=>b("svg",{...tc,width:String(t),height:t,stroke:e,"stroke-width":r?24*Number(n)/Number(t):n,class:["lucide",s].join(" "),...a},[...o.map(([e,t])=>b(e,t)),...A(i)]),rc=(e,t)=>{const n=({class:n="",children:r,...i})=>b(nc,{...i,iconNode:t,class:ec(`lucide-${Xa(Za(e))}`,`lucide-${Xa(e)}`,n)},r);return n.displayName=Za(e),n},ic=rc("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),oc=rc("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),sc=rc("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),ac=rc("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),cc=rc("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),lc=rc("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),uc=rc("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),dc=rc("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),hc=({onCreateWatchlist:e})=>{const[t,n]=It(!1),[r,i]=It(""),o=async()=>{if(r.trim())try{await e(r),i(""),n(!1)}catch(t){console.error("Error creating watchlist:",t)}},s=()=>{n(!1),i("")};return $("div",{className:"empty-watchlist-content",children:[$("div",{className:"empty-watchlist-icon",children:$(uc,{size:64})}),$("h2",{className:"empty-watchlist-title",children:"No Watchlists Yet"}),$("p",{className:"empty-watchlist-description",children:"Create your first watchlist to start tracking your favorite instruments"}),$("button",{className:"create-first-watchlist-btn",onClick:()=>n(!0),children:[$(sc,{size:16}),"Create Your First Watchlist"]}),t&&$("div",{className:"first-watchlist-form",children:[$("input",{type:"text",value:r,onChange:e=>i(e.currentTarget.value),placeholder:"Enter watchlist name",className:"first-watchlist-input",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key&&o(),"Escape"===e.key&&s()}}),$("div",{className:"first-watchlist-actions",children:[$("button",{onClick:o,className:"confirm-first-btn",children:[$(ic,{size:16}),"Create"]}),$("button",{onClick:s,className:"cancel-first-btn",children:[$(dc,{size:16}),"Cancel"]})]})]})]})},fc=({watchlists:e,activeWatchlistId:t,onWatchlistSelect:n,onCreateWatchlist:r})=>{const[i,o]=It(!1),[s,a]=It(""),[c,l]=It(""),[u,d]=It(""),[h,f]=It(""),{updateWatchlistMutation:p,deleteWatchlistMutation:m}=Ya(),v=Bt(null),y=Bt(null),g=Bt(null);Ut(()=>{i&&y.current&&v.current&&setTimeout(()=>{y.current?.scrollIntoView({behavior:"smooth",block:"nearest",inline:"end"})},50)},[i]),Ut(()=>{c&&g.current&&setTimeout(()=>{g.current?.focus()},50)},[c]);const b=()=>{l(""),d("")},_=async()=>{if(s.trim())try{o(!1),await r(s),a("")}catch(e){console.error("Error creating watchlist:",e),o(!0)}},x=async e=>{if(u.trim())try{p.mutate({id:e,name:u}),l("")}catch(t){console.error("Error updating watchlist name:",t)}};return $("div",{className:"tab-bar",ref:v,children:$("div",{className:"tabs-container",children:[e.map(r=>$("div",{className:"tab "+(t===r.id?"active":""),onClick:()=>n(r.id),children:c===r.id?$("div",{className:"tab-edit-form",onClick:e=>e.stopPropagation(),children:[$("input",{ref:g,type:"text",value:u,onChange:e=>d(e.currentTarget.value),className:"tab-edit-input",onKeyDown:e=>((e,t)=>{"Enter"===e.key&&x(t),"Escape"===e.key&&b()})(e,r.id)}),$("div",{className:"tab-edit-actions",children:[$("button",{onClick:()=>x(r.id),className:"confirm-edit-btn",children:$(ic,{size:16})}),$("button",{onClick:b,className:"cancel-edit-btn",children:$(dc,{size:16})})]})]}):$(w,{children:[$("div",{className:"tab-content",children:$("span",{className:"tab-name",children:r.name})}),$("div",{className:"tab-actions",children:[$("button",{className:"tab-action-btn edit-tab-btn",onClick:e=>{var t,n;e.stopPropagation(),t=r.id,n=r.name,l(t),d(n)},children:$(oc,{size:14})}),h===r.id?$("div",{className:"delete-confirm",onClick:e=>e.stopPropagation(),children:[$("button",{className:"confirm-delete-btn",onClick:()=>(async e=>{try{m.mutate(e),f("")}catch(t){console.error("Error deleting watchlist:",t)}})(r.id),children:$(ic,{size:16})}),$("button",{className:"cancel-delete-btn",onClick:()=>f(""),children:$(dc,{size:16})})]}):$("button",{className:"tab-action-btn close-tab-btn",onClick:t=>{t.stopPropagation(),e.length<=1||f(r.id)},disabled:e.length<=1,children:$(dc,{size:16})})]})]})},r.id)),i?$("div",{className:"add-tab-form",ref:y,children:[$("input",{type:"text",value:s,onChange:e=>a(e.currentTarget.value),placeholder:"Watchlist name",className:"add-tab-input",autoFocus:!0,onKeyDown:e=>{"Enter"===e.key&&_(),"Escape"===e.key&&(o(!1),a(""))}}),$("div",{className:"add-tab-actions",children:[$("button",{onClick:_,className:"confirm-add-btn",children:$(ic,{size:16})}),$("button",{onClick:()=>{o(!1),a("")},className:"cancel-add-btn",children:$(dc,{size:16})})]})]}):$("button",{className:"add-tab-btn",onClick:()=>{o(!0)},title:"Add new tab",children:$(sc,{size:16})})]})})};
/**
   * @license lucide-preact v0.525.0 - ISC
   *
   * This source code is licensed under the ISC license.
   * See the LICENSE file in the root directory of this source tree.
   */function pc(){return pc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},pc.apply(null,arguments)}function mc(e){return(mc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function vc(e){var t=function(e,t){if("object"!=mc(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=mc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mc(t)?t:t+""}function yc(e,t,n){return(t=vc(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function bc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gc(Object(n),!0).forEach(function(t){yc(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gc(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function wc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,s,a=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(a.push(r.value),a.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw i}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return _c(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_c(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var xc,kc={exports:{}};
/*!
  	Copyright (c) 2018 Jed Watson.
  	Licensed under the MIT License (MIT), see
  	http://jedwatson.github.io/classnames
  */var Sc,Ec=(xc||(xc=1,Sc=kc,function(){var e={}.hasOwnProperty;function t(){for(var e="",t=0;t<arguments.length;t++){var i=arguments[t];i&&(e=r(e,n(i)))}return e}function n(n){if("string"==typeof n||"number"==typeof n)return n;if("object"!=typeof n)return"";if(Array.isArray(n))return t.apply(null,n);if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]"))return n.toString();var i="";for(var o in n)e.call(n,o)&&n[o]&&(i=r(i,o));return i}function r(e,t){return t?e?e+" "+t:e+t:e}Sc.exports?(t.default=t,Sc.exports=t):window.classNames=t}()),kc.exports);const Oc=Br(Ec);var Cc=Symbol.for("react.element"),Rc=Symbol.for("react.transitional.element"),Ac=Symbol.for("react.fragment");function Nc(e){return e&&"object"===mc(e)&&(e.$$typeof===Cc||e.$$typeof===Rc)&&e.type===Ac}function Pc(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];return $n.Children.forEach(e,function(e){(null!=e||t.keepEmpty)&&(Array.isArray(e)?n=n.concat(Pc(e)):Nc(e)&&e.props?n=n.concat(Pc(e.props.children,t)):n.push(e))}),n}function Tc(e){return e instanceof HTMLElement||e instanceof SVGElement}function Mc(e){var t,n=function(e){return e&&"object"===mc(e)&&Tc(e.nativeElement)?e.nativeElement:Tc(e)?e:null}(e);return n||(e instanceof $n.Component?null===(t=$n.findDOMNode)||void 0===t?void 0:t.call($n,e):null)}var jc,Fc,Dc={exports:{}},Lc={};var Ic=(Fc||(Fc=1,Dc.exports=function(){if(jc)return Lc;jc=1;var e,t=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),a=Symbol.for("react.context"),c=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen");function m(e){if("object"==typeof e&&null!==e){var p=e.$$typeof;switch(p){case t:switch(e=e.type){case r:case o:case i:case u:case d:return e;default:switch(e=e&&e.$$typeof){case c:case a:case l:case f:case h:case s:return e;default:return p}}case n:return p}}}return e=Symbol.for("react.module.reference"),Lc.ContextConsumer=a,Lc.ContextProvider=s,Lc.Element=t,Lc.ForwardRef=l,Lc.Fragment=r,Lc.Lazy=f,Lc.Memo=h,Lc.Portal=n,Lc.Profiler=o,Lc.StrictMode=i,Lc.Suspense=u,Lc.SuspenseList=d,Lc.isAsyncMode=function(){return!1},Lc.isConcurrentMode=function(){return!1},Lc.isContextConsumer=function(e){return m(e)===a},Lc.isContextProvider=function(e){return m(e)===s},Lc.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},Lc.isForwardRef=function(e){return m(e)===l},Lc.isFragment=function(e){return m(e)===r},Lc.isLazy=function(e){return m(e)===f},Lc.isMemo=function(e){return m(e)===h},Lc.isPortal=function(e){return m(e)===n},Lc.isProfiler=function(e){return m(e)===o},Lc.isStrictMode=function(e){return m(e)===i},Lc.isSuspense=function(e){return m(e)===u},Lc.isSuspenseList=function(e){return m(e)===d},Lc.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===r||t===o||t===i||t===u||t===d||t===p||"object"==typeof t&&null!==t&&(t.$$typeof===f||t.$$typeof===h||t.$$typeof===s||t.$$typeof===a||t.$$typeof===l||t.$$typeof===e||void 0!==t.getModuleId)},Lc.typeOf=m,Lc}()),Dc.exports);var qc=Number("18.3.1".split(".")[0]),Uc=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t.filter(Boolean);return r.length<=1?r[0]:function(e){t.forEach(function(t){!function(e,t){"function"==typeof e?e(t):"object"===mc(e)&&e&&"current"in e&&(e.current=t)}(t,e)})}},zc=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r=function(){return Uc.apply(void 0,t)},i=t,o=function(e,t){return e.length!==t.length||e.every(function(e,n){return e!==t[n]})},"value"in(s=Bt({})).current&&!o(s.current.condition,i)||(s.current.value=r(),s.current.condition=i),s.current.value;var r,i,o,s};function Bc(e){return Bn(e)&&!Nc(e)}var Hc=H(null);var Wc=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some(function(e,r){return e[0]===t&&(n=r,!0)}),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var i=r[n];e.call(t,i[1],i[0])}},t}()}(),$c="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,Qc="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),Kc="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(Qc):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)};var Vc=["top","right","bottom","left","width","height","size","weight"],Gc="undefined"!=typeof MutationObserver,Jc=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,r=!1,i=0;function o(){n&&(n=!1,e()),r&&a()}function s(){Kc(o)}function a(){var e=Date.now();if(n){if(e-i<2)return;r=!0}else n=!0,r=!1,setTimeout(s,t);i=e}return a}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){$c&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Gc?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){$c&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;Vc.some(function(e){return!!~n.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),Yc=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];Object.defineProperty(e,i,{value:t[i],enumerable:!1,writable:!1,configurable:!0})}return e},Xc=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||Qc},Zc=ol(0,0,0,0);function el(e){return parseFloat(e)||0}function tl(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce(function(t,n){return t+el(e["border-"+n+"-width"])},0)}function nl(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return Zc;var r=Xc(e).getComputedStyle(e),i=function(e){for(var t={},n=0,r=["top","right","bottom","left"];n<r.length;n++){var i=r[n],o=e["padding-"+i];t[i]=el(o)}return t}(r),o=i.left+i.right,s=i.top+i.bottom,a=el(r.width),c=el(r.height);if("border-box"===r.boxSizing&&(Math.round(a+o)!==t&&(a-=tl(r,"left","right")+o),Math.round(c+s)!==n&&(c-=tl(r,"top","bottom")+s)),!function(e){return e===Xc(e).document.documentElement}(e)){var l=Math.round(a+o)-t,u=Math.round(c+s)-n;1!==Math.abs(l)&&(a-=l),1!==Math.abs(u)&&(c-=u)}return ol(i.left,i.top,a,c)}var rl="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof Xc(e).SVGGraphicsElement}:function(e){return e instanceof Xc(e).SVGElement&&"function"==typeof e.getBBox};function il(e){return $c?rl(e)?function(e){var t=e.getBBox();return ol(0,0,t.width,t.height)}(e):nl(e):Zc}function ol(e,t,n,r){return{x:e,y:t,width:n,height:r}}var sl=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=ol(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=il(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),al=function(){return function(e,t){var n,r,i,o,s,a,c,l=(r=(n=t).x,i=n.y,o=n.width,s=n.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,c=Object.create(a.prototype),Yc(c,{x:r,y:i,width:o,height:s,top:i,right:r+o,bottom:s+i,left:r}),c);Yc(this,{target:e,contentRect:l})}}(),cl=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new Wc,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Xc(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new sl(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Xc(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new al(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),ll="undefined"!=typeof WeakMap?new WeakMap:new Wc,ul=function(){return function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=Jc.getInstance(),r=new cl(t,n,this);ll.set(this,r)}}();["observe","unobserve","disconnect"].forEach(function(e){ul.prototype[e]=function(){var t;return(t=ll.get(this))[e].apply(t,arguments)}});var dl=void 0!==Qc.ResizeObserver?Qc.ResizeObserver:ul,hl=new Map;var fl=new dl(function(e){e.forEach(function(e){var t,n=e.target;null===(t=hl.get(n))||void 0===t||t.forEach(function(e){return e(n)})})});function pl(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ml(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,vc(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vl(e,t){return(vl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yl(e){return(yl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function gl(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(gl=function(){return!!e})()}function bl(e,t){if(t&&("object"==mc(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}var _l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vl(e,t)}(i,e);var t,n,r=(t=i,n=gl(),function(){var e,r=yl(t);if(n){var i=yl(this).constructor;e=Reflect.construct(r,arguments,i)}else e=r.apply(this,arguments);return bl(this,e)});function i(){return pl(this,i),r.apply(this,arguments)}return ml(i,[{key:"render",value:function(){return this.props.children}}]),i}(x);function wl(e,t){var n=e.children,r=e.disabled,i=Bt(null),o=Bt(null),s=Qt(Hc),a="function"==typeof n,c=a?n(i):n,l=Bt({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),u=!a&&Bn(c)&&function(e){var t,n;if(!e)return!1;if(Bc(e)&&qc>=19)return!0;var r=Ic.isMemo(e)?e.type.type:e.type;return!!("function"!=typeof r||null!==(t=r.prototype)&&void 0!==t&&t.render||r.$$typeof===Ic.ForwardRef)&&!!("function"!=typeof e||null!==(n=e.prototype)&&void 0!==n&&n.render||e.$$typeof===Ic.ForwardRef)}(c),d=u?function(e){if(e&&Bc(e)){var t=e;return t.props.propertyIsEnumerable("ref")?t.props.ref:t.ref}return null}(c):null,h=zc(d,i),f=function(){var e;return Mc(i.current)||(i.current&&"object"===mc(i.current)?Mc(null===(e=i.current)||void 0===e?void 0:e.nativeElement):null)||Mc(o.current)};Ht(t,function(){return f()});var p=Bt(e);p.current=e;var m=$t(function(e){var t=p.current,n=t.onResize,r=t.data,i=e.getBoundingClientRect(),o=i.width,a=i.height,c=e.offsetWidth,u=e.offsetHeight,d=Math.floor(o),h=Math.floor(a);if(l.current.width!==d||l.current.height!==h||l.current.offsetWidth!==c||l.current.offsetHeight!==u){var f={width:d,height:h,offsetWidth:c,offsetHeight:u};l.current=f;var m=c===Math.round(o)?o:c,v=u===Math.round(a)?a:u,y=bc(bc({},f),{},{offsetWidth:m,offsetHeight:v});null==s||s(y,e,r),n&&Promise.resolve().then(function(){n(y,e)})}},[]);return Ut(function(){var e,t,n=f();return n&&!r&&(e=n,t=m,hl.has(e)||(hl.set(e,new Set),fl.observe(e)),hl.get(e).add(t)),function(){return function(e,t){hl.has(e)&&(hl.get(e).delete(t),hl.get(e).size||(fl.unobserve(e),hl.delete(e)))}(n,m)}},[i.current,r]),b(_l,{ref:o},u?Hn(c,{ref:h}):c)}var xl=pn(wl);function kl(e,t){var n=e.children;return("function"==typeof n?[n]:Pc(n)).map(function(n,r){var i=(null==n?void 0:n.key)||"".concat("rc-observer-key","-").concat(r);return b(xl,pc({},e,{key:i,ref:0===r?t:void 0}),n)})}var Sl=pn(kl);function El(e){var t=Bt();t.current=e;var n=$t(function(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))},[]);return n}Sl.Collection=function(e){var t=e.children,n=e.onBatchResize,r=Bt(0),i=Bt([]),o=Qt(Hc),s=$t(function(e,t,s){r.current+=1;var a=r.current;i.current.push({size:e,element:t,data:s}),Promise.resolve().then(function(){a===r.current&&(null==n||n(i.current),i.current=[])}),null==o||o(e,t,s)},[n,o]);return b(Hc.Provider,{value:s},t)};var Ol="undefined"!=typeof window&&window.document&&window.document.createElement?zt:Ut,Cl=function(e,t){var n=Bt(!0);Ol(function(){return e(n.current)},t),Ol(function(){return n.current=!1,function(){n.current=!0}},[])},Rl=pn(function(e,t){var n=e.height,r=e.offsetY,i=e.offsetX,o=e.children,s=e.prefixCls,a=e.onInnerResize,c=e.innerProps,l=e.rtl,u=e.extra,d={},h={display:"flex",flexDirection:"column"};return void 0!==r&&(d={height:n,position:"relative",overflow:"hidden"},h=bc(bc({},h),{},yc(yc(yc(yc(yc({transform:"translateY(".concat(r,"px)")},l?"marginRight":"marginLeft",-i),"position","absolute"),"left",0),"right",0),"top",0))),b("div",{style:d},b(Sl,{onResize:function(e){e.offsetHeight&&a&&a()}},b("div",pc({style:h,className:Oc(yc({},"".concat(s,"-holder-inner"),s)),ref:t},c),o,u)))});function Al(e){var t=e.children,n=e.setRef;return Hn(t,{ref:$t(function(e){n(e)},[])})}function Nl(e,t,n){var r=wc(It(e),2),i=r[0],o=r[1],s=wc(It(null),2),a=s[0],c=s[1];return Ut(function(){var n=function(e,t,n){var r,i,o=e.length,s=t.length;if(0===o&&0===s)return null;o<s?(r=e,i=t):(r=t,i=e);var a={__EMPTY_ITEM__:!0};function c(e){return void 0!==e?n(e):a}for(var l=null,u=1!==Math.abs(o-s),d=0;d<i.length;d+=1){var h=c(r[d]);if(h!==c(i[d])){l=d,u=u||h!==c(i[d+1]);break}}return null===l?null:{index:l,multiple:u}}(i||[],e||[],t);void 0!==(null==n?void 0:n.index)&&c(e[n.index]),o(e)},[e]),[a]}Rl.displayName="Filler";var Pl=function(e){return+setTimeout(e,16)},Tl=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(Pl=function(e){return window.requestAnimationFrame(e)},Tl=function(e){return window.cancelAnimationFrame(e)});var Ml=0,jl=new Map;function Fl(e){jl.delete(e)}var Dl=function(e){var t=Ml+=1;return function n(r){if(0===r)Fl(t),e();else{var i=Pl(function(){n(r-1)});jl.set(t,i)}}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:1),t};Dl.cancel=function(e){var t=jl.get(e);return Fl(e),Tl(t)};var Ll="object"===("undefined"==typeof navigator?"undefined":mc(navigator))&&/Firefox/i.test(navigator.userAgent);const Il=function(e,t,n,r){var i=Bt(!1),o=Bt(null);var s=Bt({top:e,bottom:t,left:n,right:r});return s.current.top=e,s.current.bottom=t,s.current.left=n,s.current.right=r,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e?t<0&&s.current.left||t>0&&s.current.right:t<0&&s.current.top||t>0&&s.current.bottom;return n&&r?(clearTimeout(o.current),i.current=!1):r&&!i.current||(clearTimeout(o.current),i.current=!0,o.current=setTimeout(function(){i.current=!1},50)),!i.current&&r}};function ql(e,t,n,r,i,o,s){var a=Bt(0),c=Bt(null),l=Bt(null),u=Bt(!1),d=Il(t,n,r,i);var h=Bt(null),f=Bt(null);return[function(t){if(e){Dl.cancel(f.current),f.current=Dl(function(){h.current=null},2);var n=t.deltaX,r=t.deltaY,i=t.shiftKey,p=n,m=r;("sx"===h.current||!h.current&&i&&r&&!n)&&(p=r,m=0,h.current="sx");var v=Math.abs(p),y=Math.abs(m);null===h.current&&(h.current=o&&v>y?"x":"y"),"y"===h.current?function(e,t){if(Dl.cancel(c.current),!d(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,a.current+=t,l.current=t,Ll||n.preventDefault(),c.current=Dl(function(){var e=u.current?10:1;s(a.current*e,!1),a.current=0}))}}(t,m):function(e,t){s(t,!0),Ll||e.preventDefault()}(t,p)}},function(t){e&&(u.current=t.detail===l.current)}]}var Ul=function(){function e(){pl(this,e),yc(this,"maps",void 0),yc(this,"id",0),yc(this,"diffRecords",new Map),this.maps=Object.create(null)}return ml(e,[{key:"set",value:function(e,t){this.diffRecords.set(e,this.maps[e]),this.maps[e]=t,this.id+=1}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function zl(e){var t=parseFloat(e);return isNaN(t)?0:t}var Bl=14/15;function Hl(e){return Math.floor(Math.pow(e,.5))}function Wl(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var $l=pn(function(e,t){var n=e.prefixCls,r=e.rtl,i=e.scrollOffset,o=e.scrollRange,s=e.onStartMove,a=e.onStopMove,c=e.onScroll,l=e.horizontal,u=e.spinSize,d=e.containerSize,h=e.style,f=e.thumbStyle,p=e.showScrollBar,m=wc(It(!1),2),v=m[0],y=m[1],g=wc(It(null),2),_=g[0],w=g[1],x=wc(It(null),2),k=x[0],S=x[1],E=!r,O=Bt(),C=Bt(),R=wc(It(p),2),A=R[0],N=R[1],P=Bt(),T=function(){!0!==p&&!1!==p&&(clearTimeout(P.current),N(!0),P.current=setTimeout(function(){N(!1)},3e3))},M=o-d||0,j=d-u||0,F=Wt(function(){return 0===i||0===M?0:i/M*j},[i,M,j]),D=Bt({top:F,dragging:v,pageY:_,startTop:k});D.current={top:F,dragging:v,pageY:_,startTop:k};var L=function(e){y(!0),w(Wl(e,l)),S(D.current.top),s(),e.stopPropagation(),e.preventDefault()};Ut(function(){var e=function(e){e.preventDefault()},t=O.current,n=C.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",L,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",L)}},[]);var I=Bt();I.current=M;var q=Bt();q.current=j,Ut(function(){if(v){var e,t=function(t){var n=D.current,r=n.dragging,i=n.pageY,o=n.startTop;Dl.cancel(e);var s=O.current.getBoundingClientRect(),a=d/(l?s.width:s.height);if(r){var u=(Wl(t,l)-i)*a,h=o;!E&&l?h-=u:h+=u;var f=I.current,p=q.current,m=p?h/p:0,v=Math.ceil(m*f);v=Math.max(v,0),v=Math.min(v,f),e=Dl(function(){c(v,l)})}},n=function(){y(!1),a()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),Dl.cancel(e)}}},[v]),Ut(function(){return T(),function(){clearTimeout(P.current)}},[i]),Ht(t,function(){return{delayHidden:T}});var U="".concat(n,"-scrollbar"),z={position:"absolute",visibility:A?null:"hidden"},B={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return l?(Object.assign(z,{height:8,left:0,right:0,bottom:0}),Object.assign(B,yc({height:"100%",width:u},E?"left":"right",F))):(Object.assign(z,yc({width:8,top:0,bottom:0},E?"right":"left",0)),Object.assign(B,{width:"100%",height:u,top:F})),b("div",{ref:O,className:Oc(U,yc(yc(yc({},"".concat(U,"-horizontal"),l),"".concat(U,"-vertical"),!l),"".concat(U,"-visible"),A)),style:bc(bc({},z),h),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:T},b("div",{ref:C,className:Oc("".concat(U,"-thumb"),yc({},"".concat(U,"-thumb-moving"),v)),style:bc(bc({},B),f),onMouseDown:L}))});function Ql(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var Kl=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Vl=[],Gl={overflowY:"auto",overflowAnchor:"none"};function Jl(e,t){var n=e.prefixCls,r=void 0===n?"rc-virtual-list":n,i=e.className,o=e.height,s=e.itemHeight,a=e.fullHeight,c=void 0===a||a,l=e.style,u=e.data,d=e.children,h=e.itemKey,f=e.virtual,p=e.direction,m=e.scrollWidth,v=e.component,y=void 0===v?"div":v,g=e.onScroll,_=e.onVirtualScroll,w=e.onVisibleChange,x=e.innerProps,k=e.extraRender,S=e.styles,E=e.showScrollBar,O=void 0===E?"optional":E,C=function(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}(e,Kl),R=$t(function(e){return"function"==typeof h?h(e):null==e?void 0:e[h]},[h]),A=function(e){var t=wc(It(0),2),n=t[0],r=t[1],i=Bt(new Map),o=Bt(new Ul),s=Bt(0);function a(){s.current+=1}function c(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];a();var t=function(){var e=!1;i.current.forEach(function(t,n){if(t&&t.offsetParent){var r=t.offsetHeight,i=getComputedStyle(t),s=i.marginTop,a=i.marginBottom,c=r+zl(s)+zl(a);o.current.get(n)!==c&&(o.current.set(n,c),e=!0)}}),e&&r(function(e){return e+1})};if(e)t();else{s.current+=1;var n=s.current;Promise.resolve().then(function(){n===s.current&&t()})}}return Ut(function(){return a},[]),[function(t,n){var r=e(t);i.current.get(r),n?(i.current.set(r,n),c()):i.current.delete(r)},c,o.current,n]}(R),N=wc(A,4),P=N[0],T=N[1],M=N[2],j=N[3],F=!(!1===f||!o||!s),D=Wt(function(){return Object.values(M.maps).reduce(function(e,t){return e+t},0)},[M.id,M.maps]),L=F&&u&&(Math.max(s*u.length,D)>o||!!m),I="rtl"===p,q=Oc(r,yc({},"".concat(r,"-rtl"),I),i),U=u||Vl,z=Bt(),B=Bt(),H=Bt(),W=wc(It(0),2),$=W[0],Q=W[1],K=wc(It(0),2),V=K[0],G=K[1],J=wc(It(!1),2),Y=J[0],X=J[1],Z=function(){X(!0)},ee=function(){X(!1)},te={getKey:R};function ne(e){Q(function(t){var n=function(e){var t=e;Number.isNaN(be.current)||(t=Math.min(t,be.current));return t=Math.max(t,0),t}("function"==typeof e?e(t):e);return z.current.scrollTop=n,n})}var re=Bt({start:0,end:U.length}),ie=Bt(),oe=wc(Nl(U,R),1)[0];ie.current=oe;var se=Wt(function(){if(!F)return{scrollHeight:void 0,start:0,end:U.length-1,offset:void 0};var e;if(!L)return{scrollHeight:(null===(e=B.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:U.length-1,offset:void 0};for(var t,n,r,i=0,a=U.length,c=0;c<a;c+=1){var l=U[c],u=R(l),d=M.get(u),h=i+(void 0===d?s:d);h>=$&&void 0===t&&(t=c,n=i),h>$+o&&void 0===r&&(r=c),i=h}return void 0===t&&(t=0,n=0,r=Math.ceil(o/s)),void 0===r&&(r=U.length-1),{scrollHeight:i,start:t,end:r=Math.min(r+1,U.length-1),offset:n}},[L,F,$,U,j,o]),ae=se.scrollHeight,ce=se.start,le=se.end,ue=se.offset;re.current.start=ce,re.current.end=le,zt(function(){var e=M.getRecord();if(1===e.size){var t=Array.from(e.keys())[0],n=e.get(t),r=U[ce];if(r&&void 0===n)if(R(r)===t){var i=M.get(t)-s;ne(function(e){return e+i})}}M.resetRecord()},[ae]);var de=wc(It({width:0,height:o}),2),he=de[0],fe=de[1],pe=Bt(),me=Bt(),ve=Wt(function(){return Ql(he.width,m)},[he.width,m]),ye=Wt(function(){return Ql(he.height,ae)},[he.height,ae]),ge=ae-o,be=Bt(ge);be.current=ge;var _e=$<=0,we=$>=ge,xe=V<=0,ke=V>=m,Se=Il(_e,we,xe,ke),Ee=function(){return{x:I?-V:V,y:$}},Oe=Bt(Ee()),Ce=El(function(e){if(_){var t=bc(bc({},Ee()),e);Oe.current.x===t.x&&Oe.current.y===t.y||(_(t),Oe.current=t)}});function Re(e,t){var n=e;t?(Wn(function(){G(n)}),Ce()):ne(n)}var Ae=function(e){var t=e,n=m?m-he.width:0;return t=Math.max(t,0),t=Math.min(t,n)},Ne=El(function(e,t){t?(Wn(function(){G(function(t){return Ae(t+(I?-e:e))})}),Ce()):ne(function(t){return t+e})}),Pe=wc(ql(F,_e,we,xe,ke,!!m,Ne),2),Te=Pe[0],Me=Pe[1];!function(e,t,n){var r,i=Bt(!1),o=Bt(0),s=Bt(0),a=Bt(null),c=Bt(null),l=function(e){if(i.current){var t=Math.ceil(e.touches[0].pageX),r=Math.ceil(e.touches[0].pageY),a=o.current-t,l=s.current-r,u=Math.abs(a)>Math.abs(l);u?o.current=t:s.current=r;var d=n(u,u?a:l,!1,e);d&&e.preventDefault(),clearInterval(c.current),d&&(c.current=setInterval(function(){u?a*=Bl:l*=Bl;var e=Math.floor(u?a:l);(!n(u,e,!0)||Math.abs(e)<=.1)&&clearInterval(c.current)},16))}},u=function(){i.current=!1,r()},d=function(e){r(),1!==e.touches.length||i.current||(i.current=!0,o.current=Math.ceil(e.touches[0].pageX),s.current=Math.ceil(e.touches[0].pageY),a.current=e.target,a.current.addEventListener("touchmove",l,{passive:!1}),a.current.addEventListener("touchend",u,{passive:!0}))};r=function(){a.current&&(a.current.removeEventListener("touchmove",l),a.current.removeEventListener("touchend",u))},Cl(function(){return e&&t.current.addEventListener("touchstart",d,{passive:!0}),function(){var e;null===(e=t.current)||void 0===e||e.removeEventListener("touchstart",d),r(),clearInterval(c.current)}},[e])}(F,z,function(e,t,n,r){var i=r;return!Se(e,t,n)&&((!i||!i._virtualHandled)&&(i&&(i._virtualHandled=!0),Te({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0))}),function(e,t,n){Ut(function(){var r=t.current;if(e&&r){var i,o,s=!1,a=function(){Dl.cancel(i)},c=function e(){a(),i=Dl(function(){n(o),e()})},l=function(e){if(!e.target.draggable&&0===e.button){var t=e;t._virtualHandled||(t._virtualHandled=!0,s=!0)}},u=function(){s=!1,a()},d=function(e){if(s){var t=Wl(e,!1),n=r.getBoundingClientRect(),i=n.top,l=n.bottom;t<=i?(o=-Hl(i-t),c()):t>=l?(o=Hl(t-l),c()):a()}};return r.addEventListener("mousedown",l),r.ownerDocument.addEventListener("mouseup",u),r.ownerDocument.addEventListener("mousemove",d),function(){r.removeEventListener("mousedown",l),r.ownerDocument.removeEventListener("mouseup",u),r.ownerDocument.removeEventListener("mousemove",d),a()}}},[e])}(L,z,function(e){ne(function(t){return t+e})}),Cl(function(){function e(e){var t=_e&&e.detail<0,n=we&&e.detail>0;!F||t||n||e.preventDefault()}var t=z.current;return t.addEventListener("wheel",Te,{passive:!1}),t.addEventListener("DOMMouseScroll",Me,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",Te),t.removeEventListener("DOMMouseScroll",Me),t.removeEventListener("MozMousePixelScroll",e)}},[F,_e,we]),Cl(function(){if(m){var e=Ae(V);G(e),Ce({x:e})}},[he.width,m]);var je=function(){var e,t;null===(e=pe.current)||void 0===e||e.delayHidden(),null===(t=me.current)||void 0===t||t.delayHidden()},Fe=function(e,t,n,r,i,o,s,a){var c=Bt(),l=wc(It(null),2),u=l[0],d=l[1];return Cl(function(){if(u&&u.times<10){if(!e.current)return void d(function(e){return bc({},e)});o();var a=u.targetAlign,c=u.originAlign,l=u.index,h=u.offset,f=e.current.clientHeight,p=!1,m=a,v=null;if(f){for(var y=a||c,g=0,b=0,_=0,w=Math.min(t.length-1,l),x=0;x<=w;x+=1){var k=i(t[x]);b=g;var S=n.get(k);g=_=b+(void 0===S?r:S)}for(var E="top"===y?h:f-h,O=w;O>=0;O-=1){var C=i(t[O]),R=n.get(C);if(void 0===R){p=!0;break}if((E-=R)<=0)break}switch(y){case"top":v=b-h;break;case"bottom":v=_-f+h;break;default:var A=e.current.scrollTop;b<A?m="top":_>A+f&&(m="bottom")}null!==v&&s(v),v!==u.lastTop&&(p=!0)}p&&d(bc(bc({},u),{},{times:u.times+1,targetAlign:m,lastTop:v}))}},[u,e.current]),function(e){if(null!=e){if(Dl.cancel(c.current),"number"==typeof e)s(e);else if(e&&"object"===mc(e)){var n,r=e.align;n="index"in e?e.index:t.findIndex(function(t){return i(t)===e.key});var o=e.offset;d({times:0,index:n,offset:void 0===o?0:o,originAlign:r})}}else a()}}(z,U,M,s,R,function(){return T(!0)},ne,je);Ht(t,function(){return{nativeElement:H.current,getScrollInfo:Ee,scrollTo:function(e){var t;(t=e)&&"object"===mc(t)&&("left"in t||"top"in t)?(void 0!==e.left&&G(Ae(e.left)),Fe(e.top)):Fe(e)}}}),Cl(function(){if(w){var e=U.slice(ce,le+1);w(e,U)}},[ce,le,U]);var De=function(e,t,n,r){var i=wc(Wt(function(){return[new Map,[]]},[e,n.id,r]),2),o=i[0],s=i[1];return function(i){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,c=o.get(i),l=o.get(a);if(void 0===c||void 0===l)for(var u=e.length,d=s.length;d<u;d+=1){var h,f=e[d],p=t(f);o.set(p,d);var m=null!==(h=n.get(p))&&void 0!==h?h:r;if(s[d]=(s[d-1]||0)+m,p===i&&(c=d),p===a&&(l=d),void 0!==c&&void 0!==l)break}return{top:s[c-1]||0,bottom:s[l]}}}(U,R,M,s),Le=null==k?void 0:k({start:ce,end:le,virtual:L,offsetX:V,offsetY:ue,rtl:I,getSize:De}),Ie=function(e,t,n,r,i,o,s,a){var c=a.getKey;return e.slice(t,n+1).map(function(e,n){var a=s(e,t+n,{style:{width:r},offsetX:i});return b(Al,{key:c(e),setRef:function(t){return o(e,t)}},a)})}(U,ce,le,m,V,P,d,te),qe=null;o&&(qe=bc(yc({},c?"height":"maxHeight",o),Gl),F&&(qe.overflowY="hidden",m&&(qe.overflowX="hidden"),Y&&(qe.pointerEvents="none")));var Ue={};return I&&(Ue.dir="rtl"),b("div",pc({ref:H,style:bc(bc({},l),{},{position:"relative"}),className:q},Ue,C),b(Sl,{onResize:function(e){fe({width:e.offsetWidth,height:e.offsetHeight})}},b(y,{className:"".concat(r,"-holder"),style:qe,ref:z,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==$&&ne(t),null==g||g(e),Ce()},onMouseEnter:je},b(Rl,{prefixCls:r,height:ae,offsetX:V,offsetY:ue,scrollWidth:m,onInnerResize:T,ref:B,innerProps:x,rtl:I,extra:Le},Ie))),L&&ae>o&&b($l,{ref:pe,prefixCls:r,scrollOffset:$,scrollRange:ae,rtl:I,onScroll:Re,onStartMove:Z,onStopMove:ee,spinSize:ye,containerSize:he.height,style:null==S?void 0:S.verticalScrollBar,thumbStyle:null==S?void 0:S.verticalScrollBarThumb,showScrollBar:O}),L&&m>he.width&&b($l,{ref:me,prefixCls:r,scrollOffset:V,scrollRange:m,rtl:I,onScroll:Re,onStartMove:Z,onStopMove:ee,spinSize:ve,containerSize:he.width,horizontal:!0,style:null==S?void 0:S.horizontalScrollBar,thumbStyle:null==S?void 0:S.horizontalScrollBarThumb,showScrollBar:O}))}var Yl=pn(Jl);function Xl(e,t,{signal:n,edges:r}={}){let i,o=null;const s=null!=r&&r.includes("leading"),a=null==r||r.includes("trailing"),c=()=>{null!==o&&(e.apply(i,o),i=void 0,o=null)};let l=null;const u=()=>{null!=l&&clearTimeout(l),l=setTimeout(()=>{l=null,a&&c(),h()},t)},d=()=>{null!==l&&(clearTimeout(l),l=null)},h=()=>{d(),i=void 0,o=null},f=function(...e){if(n?.aborted)return;i=this,o=e;const t=null==l;u(),s&&t&&c()};return f.schedule=u,f.cancel=h,f.flush=()=>{d(),c()},n?.addEventListener("abort",h,{once:!0}),f}Yl.displayName="List";const Zl=new class{baseUrl="https://dev.vn.euroland.com/tools/widget.watchlist.api";async searchInstruments(e,t,n){try{return(await Ta.get(`${this.baseUrl}/Instrument/find-all`,{params:{pageIndex:e,pageSize:t,keyword:n},headers:{Authorization:`Bearer ${Ga()}`,"Content-Type":"application/json"}})).data}catch(r){if(Ta.isAxiosError(r)){const e=r.response?.data?.message||`Failed to search instruments: ${r.message}`;throw new Error(e)}throw r}}},eu=({activeWatchlistId:e,activeWatchlistInstrumentIds:t=[]})=>{const[n,r]=It(""),[i,o]=It(!1),[s,a]=It([]),[c,l]=It(!1),u=Bt(null),{addInstrumentMutation:d}=Ya(),h=$t(Xl(async e=>{if(o(!0),e.trim())try{o(!0);const t=await Zl.searchInstruments(1,25,e);a(t?.data?.items||[])}catch(t){console.error("Error searching instruments:",t),a([])}finally{o(!1)}else a([])},500),[]);Ut(()=>{h(n)},[n,h]);return $("div",{className:"add-instrument-section",children:[$("h4",{className:"add-section-title",children:"Add Symbols"}),$("div",{className:"search-container",children:[$(ac,{size:16,className:"search-icon"}),$("input",{type:"text",value:n,ref:u,onChange:e=>{const t=e.currentTarget.value;r(t)},onFocus:()=>l(!0),onBlur:()=>l(!1),placeholder:"Search instruments to add...",className:"search-input"})]}),c&&n.length>1&&$("div",{className:"search-results",onMouseDown:e=>e.preventDefault(),children:i?$("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"300px"},children:$("div",{className:"spinner small"})}):s.length>0?$(Yl,{data:s,height:300,itemHeight:60,itemKey:"instrumentId",children:n=>{return $("div",{className:"search-result-item",children:[$("div",{className:"instrument-info",children:[$("div",{className:"symbol-info",children:[$("span",{className:"symbol",children:n.ticker}),$("span",{className:"name",children:n.name})]}),$("span",{className:"abbreviation",children:n.abbreviation??"--"})]}),$("button",{className:"add-instrument-btn",onClick:()=>(async t=>{if(e)try{d.mutate({instrumentId:parseInt(t.instrumentId||"0"),watchlistId:e})}catch(n){console.error("Error adding instrument to watchlist: ",n)}finally{r(""),l(!1),u.current?.blur()}})(n),disabled:(i=n.instrumentId||"0",t.some(e=>e.toString()===i)),children:$(sc,{size:16})})]},n.instrumentId);var i}}):$("div",{className:"search-results-empty",children:[$("p",{children:"No instruments found"}),$("p",{children:"Try a different search term"})]})})]})},tu=(function(e){for(var t=new Map,n=[],r=[],i=Array.isArray(e)?e[0]:e||"",o=1;o<arguments.length;o++){var s=arguments[o];s&&s.definitions?r.push(s):i+=s,i+=arguments[0][o]}r.unshift(st(i));for(var a=0;a<r.length;a++)for(var c=0;c<r[a].definitions.length;c++){var l=r[a].definitions[c];if(l.kind===J){var u=l.name.value,d=it(l);t.has(u)||(t.set(u,d),n.push(l))}else n.push(l)}return st({kind:V,definitions:n})})`
query Ticker($ids: [Int!]!, $adjClose: Boolean, $toCurrency: String) {
  instrumentByIds(ids: $ids, exchangeCurrency: $toCurrency, adjClose: $adjClose) {
    shareName
    id
    symbol
    market {
      translation {
        cultureName
        value
      }
      status {
        isOpened
        remainingTime
      }
    }
    currency {
      code
      name
    }
    currentPrice {
      open
      date
      bid
      ask
      high
      low
      volume
      officialClose
      officialCloseDate
      # Chỉ query tickerData nếu cần thiết
      tickerData {
        last
        change
        changePercentage
        prevClose
      }
    }
    fifty_two_weeks: performance(period: FIFTY_TWO_WEEKS) {
      highest
      lowest
      changePercentage
    }
  }
}
`,nu=({children:e,type:t="loading"})=>$("div",{className:"instruments-section",children:$("div",{className:"instruments-table-container",children:$("table",{className:"instruments-table",children:[$("thead",{children:$("tr",{children:[$("th",{className:"table-header",children:"Symbol"}),$("th",{className:"table-header",children:"Last"}),$("th",{className:"table-header",children:"Change"}),$("th",{className:"table-header",style:{textAlign:"end"},children:"Change%"})]})}),$("tbody",{children:"loading"===t?Array.from({length:5},(e,t)=>$("tr",{className:"skeleton-row",children:[$("td",{className:"skeleton-cell",children:$("div",{className:"skeleton-symbol",children:[$("div",{className:"skeleton-line skeleton-symbol-text"}),$("div",{className:"skeleton-line skeleton-market-text"})]})}),$("td",{className:"skeleton-cell",children:$("div",{className:"skeleton-line skeleton-price"})}),$("td",{className:"skeleton-cell",children:$("div",{className:"skeleton-line skeleton-change"})}),$("td",{className:"skeleton-cell",children:$("div",{className:"skeleton-line skeleton-change-percent"})})]},t)):$("tr",{children:$("td",{colSpan:4,children:e})})})]})})}),ru=({activeWatchlist:e})=>{const t=e.id,{instruments:n,isLoading:r}=(({activeWatchlist:e})=>{const t=e?.instrumentIds||[],{data:n=[],isLoading:r}=wo(()=>({queryKey:["instruments",t.join(",")],staleTime:0,queryFn:async()=>{const e=await Vn.query(tu,{adjClose:!1,ids:t}),n=e.data?.instrumentByIds,r=n?.filter(e=>null!==e).map(e=>{const t=e?.market?.status?.isOpened?"Open":"Close";return{id:e?.id?.toString(),instrumentId:e?.id?.toString(),symbol:e?.symbol,name:e?.shareName,price:e?.currentPrice?.tickerData?.last,change:e?.currentPrice?.tickerData?.change,last:e?.currentPrice?.tickerData?.last,high:e?.currentPrice?.high,low:e?.currentPrice?.low,hightOfWeek:e?.fifty_two_weeks?.highest,changePercent:e?.currentPrice?.tickerData?.changePercentage,volume:e?.currentPrice?.volume,currency:e?.currency?.code,market:e?.market?.translation?.value,marketStatus:t,fiftyTwoWeeks:e?.fifty_two_weeks}});return r}}));return{instruments:n,isLoading:r}})({activeWatchlist:e}),{removeInstrumentMutation:i}=Ya(),{selectedInstrument:o,setSelectedInstrument:s}=(()=>{const[e,t]=It();return Ut(()=>{const t=window.EurolandAppContext;if(t)return t.registerCommandHandler("instrument-selected",()=>e),e&&t.emit("instrument-selected",e),()=>{t.unregisterCommandHandler("instrument-selected",()=>e)}},[e]),{selectedInstrument:e,setSelectedInstrument:t}})();return r?$(nu,{type:"loading",children:$("div",{className:"loading-state",children:[$("div",{className:"loading-icon",children:"📊"}),$("p",{className:"loading-title",children:"Loading instruments..."}),$("p",{className:"loading-subtitle",children:"Please wait while we fetch your data"})]})}):0===n.length?$(nu,{type:"empty",children:$("div",{className:"empty-state",children:[$("div",{className:"empty-icon",children:"📈"}),$("p",{className:"empty-title",children:"No instruments in this watchlist"}),$("p",{className:"empty-subtitle",children:"Use the search below to add instruments"})]})}):$("div",{className:"instruments-section",children:$("div",{className:"instruments-table-container",children:$("table",{className:"instruments-table",children:[$("thead",{children:$("tr",{children:[$("th",{className:"table-header",children:"Symbol"}),$("th",{className:"table-header",children:"Last"}),$("th",{className:"table-header",children:"Change"}),$("th",{className:"table-header",style:{textAlign:"end"},children:"Change%"})]})}),$("tbody",{children:n.map(e=>$("tr",{className:"table-row "+(o?.id===e.id?"selected":""),onClick:()=>(e=>{const t={id:e.id,symbol:e.symbol};s(t)})(e),children:[$("td",{className:"cell symbol",children:[$("span",{className:"symbol-text",children:e.symbol}),$("span",{className:"market-text",children:e.market})]}),$("td",{className:"cell price",children:e?.last?.toFixed(2)?e?.last?.toFixed(2)+(e.currency?" "+e.currency:" --"):"--"}),$("td",{className:"cell change "+(e.change>=0?"positive":"negative"),children:[e.change>=0?"+":"",e.change?.toFixed(2)??"--"]}),$("td",{className:"cell change-percent "+(e.changePercent>=0?"positive":"negative"),children:[$("span",{className:"change-value",children:[e.changePercent>=0?"+":"",e.changePercent?.toFixed(2)?e.changePercent?.toFixed(2)+"%":"--"]}),$("button",{className:"remove-btn",onClick:n=>(async(e,n)=>{if(e.stopPropagation(),t)try{i.mutate({watchlistId:t,instrumentId:parseInt(n)})}catch(r){console.error("Error removing instrument from watchlist: ",r)}})(n,e.id),children:$(lc,{size:16})})]})]},e.id))})]})})})},iu=()=>{const{watchlistsQuery:e,createWatchlistMutation:t}=Ya(),{data:n,isLoading:r,error:i}=e,{activeWatchlistId:o,setActiveWatchlistId:s,activeWatchlist:a,watchlistsArray:c}=(({watchlists:e})=>{const[t,n]=It(null),r=Wt(()=>e||[],[e]);Ut(()=>{r.length>0&&null===t&&n(r[0].id),t&&r.length>0&&(r.some(e=>e.id===t)||n(r[0].id))},[r,t]);const i=Wt(()=>t&&0!==r.length&&r.find(e=>e.id===t)||null,[r,t]);return{activeWatchlistId:t,setActiveWatchlistId:n,activeWatchlist:i,watchlistsArray:r}})({watchlists:n?.data||[]}),l=async e=>{if(e.trim())try{t.mutate(e)}catch(n){console.error("Error creating watchlist: ",n)}};return r?$("div",{className:"loading-spinner",children:$("div",{className:"spinner"})}):i?$("div",{className:"error-message",children:[$("div",{children:"Error loading watchlists"}),$("div",{children:"Let's try again"})]}):r||0!==c.length?$("div",{className:"watchlist-container",children:[$(fc,{watchlists:c,activeWatchlistId:o,onWatchlistSelect:s,onCreateWatchlist:l}),$(eu,{activeWatchlistId:o,activeWatchlistInstrumentIds:a?.instrumentIds}),$("div",{className:"content-area",children:a&&$(ru,{activeWatchlist:a})})]}):$(hc,{onCreateWatchlist:l})},ou=({watchlist:e,watchlistsRefetch:t})=>{const{addInstrumentMutation:n}=Ya(),r=e.instrumentIds.find(e=>e===parseInt(window.xprops?.instrumentId||"0"));return $("div",{className:"watchlist-item "+(r?"watchlist-item-disabled":""),children:[$("div",{className:"watchlist-info",children:[$("div",{className:"watchlist-name",children:[e.isDefault&&$(cc,{className:"default-star",size:14}),e.name]}),$("div",{className:"watchlist-count",children:[e.instrumentIds?.length||0," instruments"]})]}),$("button",{className:"add-btn",onClick:()=>(e=>{if(!r)try{n.mutate({watchlistId:e,instrumentId:parseInt(window.xprops?.instrumentId||"0")}),t()}catch(i){console.error("Error adding instrument to watchlist:",i)}})(e.id),disabled:n.isLoading||!!r,children:n.isLoading?$("div",{className:"loading-spinner small"}):$(r?ic:sc,{size:16})})]},e.id)};function su(){const e=Eo(),[t,n]=It(""),[r,i]=It(!1),[o,s]=It(""),{createWatchlistMutation:a,watchlistsQuery:c}=Ya();if(!e.isAuthenticated)return null;const l=c.data?.data.filter(e=>e.name.toLowerCase().includes(t.toLowerCase()))||[],u=e=>{try{o.trim()&&(a.mutate(e),c.refetch(),s(""),i(!1))}catch(t){console.error("Error creating new watchlist:",t)}},d=()=>{s(""),i(!1)};return $("div",{className:"watchlist-modal",onClick:e=>e.stopPropagation(),children:$("div",{className:"modal-content",children:[$("div",{className:"search-section",children:$("div",{className:"search-container",children:[$(ac,{className:"search-icon",size:16}),$("input",{type:"text",placeholder:"Search watchlists...",value:t,onChange:e=>{n(e.currentTarget.value)},className:"search-input"})]})}),$("div",{className:"watchlists-section",children:[$("div",{className:"section-header",children:[$("h4",{children:"Select Watchlist"}),$("button",{className:"create-new-btn",onClick:()=>i(!0),disabled:r,children:[$(sc,{size:14}),"New Watchlist"]})]}),$("div",{className:"watchlists-list",children:[r&&$("div",{className:"create-watchlist-form",children:[$("input",{type:"text",placeholder:"Watchlist name",value:o,onChange:e=>{s(e.currentTarget.value)},className:"create-input",onKeyDown:e=>{"Enter"===e.key&&u(o),"Escape"===e.key&&d()},autoFocus:!0}),$("div",{className:"create-actions",children:[$("button",{className:"confirm-btn",onClick:()=>u(o),children:$(ic,{size:14})}),$("button",{className:"cancel-btn",onClick:d,children:$(dc,{size:14})})]})]}),c.isLoading?$("div",{style:{display:"flex",justifyContent:"center",alignItems:"center"},children:$("div",{className:"loading-spinner"})}):l.map(e=>$(ou,{watchlist:e,watchlistsRefetch:c.refetch},e.id)),0===l.length&&t&&$("div",{className:"empty-state",children:$("p",{children:['No watchlists found matching "',t,'"']})})]})]})]})})}const au=new URLSearchParams(window.location.search).toString();window.euroland?.createComponent("WatchlistAddInstrument",{tag:"watchlist-add-instrument",url:"/tools/sharegraph3-realtime/watchlist-add-instrument"+(au.length?`?${au}`:""),dimensions:{width:"500px",height:"600px"},template:{name:"modal",clickOverlayToClose:!1,styles:{position:"fixed",bottom:"0px"}},props:{instrumentId:{type:"string",required:!0}}}),window.addEventListener("error",e=>{if(e.message.includes("ResizeObserver loop"))return e.stopImmediatePropagation(),!1});const cu=new class{constructor(e={}){this.queryCache=e.queryCache||new Tr,this.mutationCache=e.mutationCache||new jr,this.logger=e.logger||Rr,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[],this.mountCount=0}mount(){this.mountCount++,1===this.mountCount&&(this.unsubscribeFocus=_r.subscribe(()=>{_r.isFocused()&&(this.resumePausedMutations(),this.queryCache.onFocus())}),this.unsubscribeOnline=xr.subscribe(()=>{xr.isOnline()&&(this.resumePausedMutations(),this.queryCache.onOnline())}))}unmount(){var e,t;this.mountCount--,0===this.mountCount&&(null==(e=this.unsubscribeFocus)||e.call(this),this.unsubscribeFocus=void 0,null==(t=this.unsubscribeOnline)||t.call(this),this.unsubscribeOnline=void 0)}isFetching(e,t){const[n]=ir(e,t);return n.fetchStatus="fetching",this.queryCache.findAll(n).length}isMutating(e){return this.mutationCache.findAll({...e,fetching:!0}).length}getQueryData(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data}ensureQueryData(e,t,n){const r=rr(e,t,n),i=this.getQueryData(r.queryKey);return i?Promise.resolve(i):this.fetchQuery(r)}getQueriesData(e){return this.getQueryCache().findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){const r=this.queryCache.find(e),i=function(e,t){return"function"==typeof e?e(t):e}(t,null==r?void 0:r.state.data);if(void 0===i)return;const o=rr(e),s=this.defaultQueryOptions(o);return this.queryCache.build(this,s).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Ar.batch(()=>this.getQueryCache().findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state}removeQueries(e,t){const[n]=ir(e,t),r=this.queryCache;Ar.batch(()=>{r.findAll(n).forEach(e=>{r.remove(e)})})}resetQueries(e,t,n){const[r,i]=ir(e,t,n),o=this.queryCache,s={type:"active",...r};return Ar.batch(()=>(o.findAll(r).forEach(e=>{e.reset()}),this.refetchQueries(s,i)))}cancelQueries(e,t,n){const[r,i={}]=ir(e,t,n);void 0===i.revert&&(i.revert=!0);const o=Ar.batch(()=>this.queryCache.findAll(r).map(e=>e.cancel(i)));return Promise.all(o).then(er).catch(er)}invalidateQueries(e,t,n){const[r,i]=ir(e,t,n);return Ar.batch(()=>{var e,t;if(this.queryCache.findAll(r).forEach(e=>{e.invalidate()}),"none"===r.refetchType)return Promise.resolve();const n={...r,type:null!=(e=null!=(t=r.refetchType)?t:r.type)?e:"active"};return this.refetchQueries(n,i)})}refetchQueries(e,t,n){const[r,i]=ir(e,t,n),o=Ar.batch(()=>this.queryCache.findAll(r).filter(e=>!e.isDisabled()).map(e=>{var t;return e.fetch(void 0,{...i,cancelRefetch:null==(t=null==i?void 0:i.cancelRefetch)||t,meta:{refetchPage:r.refetchPage}})}));let s=Promise.all(o).then(er);return null!=i&&i.throwOnError||(s=s.catch(er)),s}fetchQuery(e,t,n){const r=rr(e,t,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);const o=this.queryCache.build(this,i);return o.isStaleByTime(i.staleTime)?o.fetch(i):Promise.resolve(o.state.data)}prefetchQuery(e,t,n){return this.fetchQuery(e,t,n).then(er).catch(er)}fetchInfiniteQuery(e,t,n){const r=rr(e,t,n);return r.behavior=Fr(),this.fetchQuery(r)}prefetchInfiniteQuery(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(er).catch(er)}resumePausedMutations(){return this.mutationCache.resumePausedMutations()}getQueryCache(){return this.queryCache}getMutationCache(){return this.mutationCache}getLogger(){return this.logger}getDefaultOptions(){return this.defaultOptions}setDefaultOptions(e){this.defaultOptions=e}setQueryDefaults(e,t){const n=this.queryDefaults.find(t=>cr(e)===cr(t.queryKey));n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})}getQueryDefaults(e){if(!e)return;const t=this.queryDefaults.find(t=>lr(e,t.queryKey));return null==t?void 0:t.defaultOptions}setMutationDefaults(e,t){const n=this.mutationDefaults.find(t=>cr(e)===cr(t.mutationKey));n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})}getMutationDefaults(e){if(!e)return;const t=this.mutationDefaults.find(t=>lr(e,t.mutationKey));return null==t?void 0:t.defaultOptions}defaultQueryOptions(e){if(null!=e&&e._defaulted)return e;const t={...this.defaultOptions.queries,...this.getQueryDefaults(null==e?void 0:e.queryKey),...e,_defaulted:!0};return!t.queryHash&&t.queryKey&&(t.queryHash=ar(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.useErrorBoundary&&(t.useErrorBoundary=!!t.suspense),t}defaultMutationOptions(e){return null!=e&&e._defaulted?e:{...this.defaultOptions.mutations,...this.getMutationDefaults(null==e?void 0:e.mutationKey),...e,_defaulted:!0}}clear(){this.queryCache.clear(),this.mutationCache.clear()}}({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5}}});class lu extends HTMLElement{container=null;connectedCallback(){this.attachShadow({mode:"open"});const e=document.createElement("style");e.textContent=Gn,e.textContent+=".content-area {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .watchlist-container {\r\n    margin: 0;\r\n    border-radius: 0;\r\n  }\r\n}\r\n/* Loading and Error States */\r\n.loading-spinner {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  color: #6c757d;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.spinner {\r\n  border: 4px solid #f3f3f3;\r\n  border-top: 4px solid #3498db;\r\n  border-radius: 50%;\r\n  width: 50px;\r\n  height: 50px;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.spinner.small {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.error-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  font-size: 1.2rem;\r\n  padding: 1rem;\r\n  text-align: center;\r\n}",e.textContent+="/* Tab Bar Styles */\n.tab-bar {\n  border-bottom: 1px solid #e1e3e6;\n  padding: 0;\n  overflow-x: auto;\n  overflow-y: hidden;\n}\n\n.tabs-container {\n  display: flex;\n  align-items: flex-end;\n  gap: 2px;\n  min-width: min-content;\n}\n\n.tab {\n  display: flex;\n  align-items: center;\n  min-width: 120px;\n  max-width: 250px;\n  height: 30px;\n  background: #fff;\n  border: 1px solid #e1e3e6;\n  border-bottom: none;\n  border-radius: 4px 4px 0 0;\n  cursor: pointer;\n  transition: all 0.2s;\n  position: relative;\n  padding: 0 8px;\n  margin-bottom: 1px;\n}\n\n.tab:hover {\n  background: #dee2e6;\n}\n\n.tab.active {\n  background: #e9ecef;\n  border-color: #d1d4dc;\n  margin-bottom: 0;\n  z-index: 1;\n}\n\n.tab-content {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  flex: 1;\n  min-width: 0;\n}\n\n.tab-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #131722;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  flex: 1;\n}\n\n.tab-star {\n  color: #ffc107;\n  flex-shrink: 0;\n}\n\n.tab-count {\n  font-size: 12px;\n  color: #6c757d;\n  flex-shrink: 0;\n}\n\n.tab-actions {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n  opacity: 0;\n  transition: opacity 0.2s;\n  margin-left: 16px;\n}\n\n.tab:hover .tab-actions {\n  opacity: 1;\n}\n\n.tab.active .tab-actions {\n  opacity: 1;\n}\n\n.tab-action-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n  border-radius: 3px;\n  background: transparent;\n  color: #6c757d;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\nbutton.tab-action-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n.tab-action-btn:hover {\n  background: rgba(0, 0, 0, 0.1);\n  color: #131722;\n}\n\n.close-tab-btn:hover {\n  background: rgba(244, 67, 54, 0.1);\n  color: #f44336;\n}\n\n.tab-action-btn:disabled {\n  opacity: 0.3;\n  cursor: not-allowed;\n}\n\n.tab-action-btn:disabled:hover {\n  background: transparent;\n  color: #6c757d;\n}\n\n/* Tab Edit Form */\n.tab-edit-form {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  flex: 1;\n  padding: 2px;\n}\n\n.tab-edit-input {\n  flex: 1;\n  padding: 4px 6px;\n  border: 1px solid #2962ff;\n  border-radius: 3px;\n  background: #ffffff;\n  color: #131722;\n  font-size: 14px;\n  font-weight: 500;\n  min-width: 80px;\n}\n\n.tab-edit-input:focus {\n  outline: none;\n  border-color: #2962ff;\n}\n\n.tab-edit-actions {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n  flex-shrink: 0;\n}\n\n.confirm-edit-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #00c853;\n  border-radius: 3px;\n  background: transparent;\n  color: #00c853;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  flex-shrink: 0;\n}\n\nbutton.confirm-edit-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n.confirm-edit-btn:hover {\n  background: rgba(0, 200, 83, 0.1);\n}\n\n.cancel-edit-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #6c757d;\n  border-radius: 3px;\n  background: transparent;\n  color: #6c757d;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  flex-shrink: 0;\n}\n\nbutton.cancel-edit-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n.cancel-edit-btn:hover {\n  background: rgba(108, 117, 125, 0.1);\n}\n\n/* Delete Confirmation */\n.delete-confirm {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n}\n\n.confirm-delete-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 3px;\n  border:1px solid #f44336;\n  color: #f44336;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\nbutton.confirm-delete-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n.cancel-delete-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 3px;\n  border:1px solid #6c757d;\n  color: #6c757d;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\nbutton.cancel-delete-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n/* Add Tab */\n.add-tab-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #fff;\n  border: 1px solid #e1e3e6;\n  border-bottom: none;\n  border-radius: 4px 4px 0 0;\n  cursor: pointer;\n  transition: all 0.2s;\n  color: #6c757d;\n  margin-bottom: 1px;\n  flex-shrink: 0;\n}\n\nbutton.add-tab-btn {\n  width: 30px;\n  height: 30px;\n  padding: 0;\n}\n\n.add-tab-btn:hover {\n  background: #dee2e6;\n  color: #131722;\n}\n\n.add-tab-form {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  min-width: 200px;\n  height: 30px;\n  background: #ffffff;\n  border: 1px solid #2962ff;\n  border-bottom: none;\n  border-radius: 4px 4px 0 0;\n  padding: 0 8px;\n  margin-bottom: 1px;\n  flex-shrink: 0;\n}\n\n.add-tab-input {\n  flex: 1;\n  padding: 4px 6px;\n  border: none;\n  background: transparent;\n  color: #131722;\n  font-size: 14px;\n  font-weight: 500;\n  min-width: 100px; /* Ensure minimum width */\n}\n\n.add-tab-input:focus {\n  outline: none;\n}\n\n.add-tab-input::placeholder {\n  color: #6c757d;\n}\n\n.add-tab-actions {\n  display: flex;\n  align-items: center;\n  gap: 2px;\n  flex-shrink: 0;\n}\n\n.confirm-add-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #00c853;\n  border-radius: 3px;\n  color: #00c853;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  flex-shrink: 0;\n}\n\nbutton.confirm-add-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n.cancel-add-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #6c757d;\n  border-radius: 3px;\n  color: #6c757d;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  flex-shrink: 0;\n}\n\nbutton.cancel-add-btn {\n  width: 20px;\n  height: 20px;\n  padding: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .tab {\n    min-width: 120px;\n    max-width: 180px;\n  }\n}\n\n@media (max-width: 480px) {\n  .tab {\n    min-width: 120px;\n    max-width: 120px;\n    padding: 0 4px;\n  }\n\n  .tab-name {\n    font-size: 12px;\n  }\n\n  .tab-count {\n    display: none;\n  }\n}\n",e.textContent+=".empty-watchlist-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.empty-watchlist-icon {\r\n  margin-bottom: 8px;\r\n  color: #6c757d;\r\n  opacity: 0.6;\r\n}\r\n\r\n.empty-watchlist-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.empty-watchlist-description {\r\n  margin: 0 0 20px 0;\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n  line-height: 1.5;\r\n}\r\n\r\n.create-first-watchlist-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 16px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.create-first-watchlist-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(41, 98, 255, 0.3);\r\n}\r\n\r\n.first-watchlist-form {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  width: 100%;\r\n  max-width: 300px;\r\n  padding: 24px;\r\n  background: white;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.first-watchlist-input {\r\n  padding: 8px 12px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 8px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 16px;\r\n  transition: border-color 0.2s;\r\n}\r\n\r\n.first-watchlist-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n  box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);\r\n}\r\n\r\n.first-watchlist-input::placeholder {\r\n  color: #6c757d;\r\n}\r\n\r\n.first-watchlist-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.confirm-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #00c853;\r\n  color: #00c853;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n.cancel-first-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  flex: 1;\r\n  padding: 6px 10px;\r\n  border: 1px solid #6c757d;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  justify-content: center;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 480px) {\r\n  /* Empty Watchlist State - Mobile */\r\n  .empty-watchlist-state {\r\n    padding: 20px 16px;\r\n  }\r\n\r\n  .empty-watchlist-title {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .empty-watchlist-description {\r\n    font-size: 14px;\r\n    margin-bottom: 24px;\r\n  }\r\n\r\n  .create-first-watchlist-btn {\r\n    padding: 10px 20px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .first-watchlist-form {\r\n    padding: 20px;\r\n    max-width: 280px;\r\n  }\r\n\r\n  .first-watchlist-actions {\r\n    gap: 8px;\r\n  }\r\n}\r\n",e.textContent+="/* Add Instrument Section */\r\n.add-instrument-section {\r\n  position: relative;\r\n  width: 100%;\r\n  padding: 8px 0;\r\n  border-top: 1px solid #e1e3e6;\r\n}\r\n\r\n.add-section-title {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #131722;\r\n}\r\n\r\n.search-container {\r\n  position: relative;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #6c757d;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 10px 8px 10px 32px;\r\n  border: 1px solid #d1d4dc;\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  color: #131722;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: #2962ff;\r\n}\r\n\r\n.search-results {\r\n  position: absolute;\r\n  top: 84px;\r\n  left: 0;\r\n  right: 0;\r\n  border: 1px solid #e1e3e6;\r\n  border-radius: 4%;\r\n  background: #ffffff;\r\n  margin-bottom: 16px;\r\n  z-index: 999;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Empty State */\r\n.search-results-empty {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 24px;\r\n  color: var(--text-secondary);\r\n  text-align: center;\r\n  min-height: 120px;\r\n}\r\n\r\n.search-results-empty-icon {\r\n  font-size: 24px;\r\n  margin-bottom: 8px;\r\n  color: #d1d4dc;\r\n}\r\n\r\n.search-results-empty-text {\r\n  font-size: 14px;\r\n  color: #6c757d;\r\n}\r\n\r\n/* Virtual List Styles */\r\n.search-results .rc-virtual-list {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-results .rc-virtual-list-holder-inner {\r\n  border-radius: 6px;\r\n}\r\n\r\n.search-result-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  cursor: pointer;\r\n  padding: 12px 16px;\r\n  gap: 4px;\r\n  border-bottom: 1px solid #f1f3f4;\r\n  transition: background-color 0.2s;\r\n  height: 60px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.search-result-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.search-result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.instrument-info {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .symbol {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 60px;\r\n}\r\n\r\n.symbol-info {\r\n  display: flex;\r\n  align-items: left;\r\n  flex-direction: column;\r\n}\r\n\r\n.instrument-info .name {\r\n  font-size: 12px;\r\n  color: #6c757d;\r\n  flex: 1;\r\n}\r\n\r\n.instrument-info .abbreviation {\r\n  font-weight: 600;\r\n  color: #131722;\r\n  min-width: 80px;\r\n}\r\n\r\n.instrument-info .change {\r\n  font-weight: 500;\r\n  min-width: 100px;\r\n}\r\n\r\n.instrument-info .change.positive {\r\n  color: #00c853;\r\n}\r\n\r\n.instrument-info .change.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.add-instrument-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #a4a6ac;\r\n  border: none;\r\n  border-radius: 4px;\r\n  padding-left: 16px;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\nbutton.add-instrument-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  padding: 0;\r\n}\r\n\r\nbutton.add-instrument-btn:hover {\r\n  background: #d8dbe2;\r\n  color: white;\r\n}\r\n\r\nbutton.add-instrument-btn:disabled {\r\n  background: #d1d4dc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .instrument-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n\r\n  .instrument-info .symbol,\r\n  .instrument-info .price,\r\n  .instrument-info .change {\r\n    min-width: auto;\r\n  }\r\n}\r\n",e.textContent+="/* Instruments Section */\n.instruments-section {\n  flex: 1;\n  padding: 10px 0 0 0;\n  display: flex;\n  flex-direction: column;\n}\n\n.instruments-table-container {\n  width: 100%;\n  border: 1px solid #e1e3e6;\n  border-radius: 6px;\n  overflow-x: auto;\n}\n\n.instruments-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: #ffffff;\n  table-layout: fixed;\n  min-width: 600px; /* Minimum width to prevent column collapse */\n}\n\n.instruments-table thead {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n}\n\n.table-header {\n  background: #f8f9fa;\n  padding: 8px 12px;\n  font-weight: 600;\n  font-size: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  color: #6c757d;\n  border-bottom: 1px solid #e1e3e6;\n  text-align: left;\n  white-space: nowrap;\n}\n\n/* Column width distribution */\n.instruments-table th:nth-child(1) { /* Symbol */\n  width: 35%;\n  min-width: 120px;\n}\n\n.instruments-table th:nth-child(2) { /* Last Price */\n  width: 25%;\n  min-width: 100px;\n}\n\n.instruments-table th:nth-child(3) { /* Change */\n  width: 20%;\n  min-width: 80px;\n}\n\n.instruments-table th:nth-child(4) { /* Change% */\n  width: 20%;\n  min-width: 100px;\n}\n\n.table-row {\n  display: table-row;\n  position: relative;\n  cursor: pointer;\n  border-bottom: 1px solid #f1f3f4;\n  transition: background-color 0.2s;\n}\n\n.table-row td {\n  display: table-cell;\n  padding: 8px 12px;\n  vertical-align: middle;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.header-cell {\n  display: flex;\n  align-items: center;\n}\n\n.symbol {\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.symbol-text {\n  font-weight: 600;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n}\n\n.positive {\n  color: #28a745;\n}\n\n.negative {\n  color: #dc3545;\n}\n\n.remove-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: #dc3545;\n  padding: 0;\n}\n\n.table-row:hover .remove-btn {\n  visibility: visible;\n}\n\n.table-row.selected {\n  background: #e8f0fe;\n}\n\n.table-row:last-child {\n  border-bottom: none;\n}\n\n.cell {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.cell.symbol {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 2px;\n}\n\n.symbol-text {\n  font-weight: 600;\n  color: #131722;\n}\n\n.market-text {\n  font-size: 11px;\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.name {\n  color: #6c757d;\n  font-weight: 400;\n}\n\n.cell.price {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.change {\n  font-weight: 500;\n}\n\n.cell.change-percent {\n  text-align: end;\n  font-weight: 500;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  gap: 8px;\n}\n\n.change-value {\n  flex: 1;\n  text-align: right;\n}\n\n.cell.change.positive {\n  color: #26a69a;\n}\n\n.cell.change.negative {\n  color: #f44336;\n}\n\n.cell.change-percent.positive {\n  color: #26a69a;\n}\n\n.cell.change-percent.negative {\n  color: #f44336;\n}\n\n.cell.high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.low {\n  color: #131722;\n  font-weight: 600;\n}\n\n.cell.week-high {\n  color: #131722;\n  font-weight: 600;\n}\n\n.remove-btn {\n  background: none;\n  visibility: hidden;\n  position: static;\n  background: white;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  color: #6c757d;\n  transition: all 0.2s;\n  flex-shrink: 0;\n}\n\n.remove-btn:hover {\n  background: rgba(243, 147, 140);\n  color: #f44336;\n}\n\n.delete-confirm {\n  display: flex;\n  gap: 4px;\n}\n\n.confirm-delete-btn,\n.cancel-delete-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s;\n}\n\n.confirm-delete-btn {\n  color: #f44336;\n}\n\n.confirm-delete-btn:hover {\n  background: rgba(244, 67, 54, 0.1);\n}\n\n.cancel-delete-btn {\n  color: #6c757d;\n}\n\n.cancel-delete-btn:hover {\n  background: rgba(108, 117, 125, 0.1);\n}\n\n/* Skeleton Loading Animations */\n@keyframes shimmer {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Skeleton Rows */\n.skeleton-row {\n  border-bottom: 1px solid #f1f3f4;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.skeleton-row:hover {\n  background: transparent;\n}\n\n.skeleton-cell {\n  padding: 12px;\n  vertical-align: middle;\n}\n\n.skeleton-line {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200px 100%;\n  animation: shimmer 1.5s infinite;\n  border-radius: 4px;\n  height: 16px;\n}\n\n.skeleton-symbol {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.skeleton-symbol-text {\n  width: 80px;\n  height: 18px;\n}\n\n.skeleton-market-text {\n  width: 60px;\n  height: 12px;\n}\n\n.skeleton-price {\n  width: 70px;\n}\n\n.skeleton-change {\n  width: 50px;\n}\n\n.skeleton-change-percent {\n  width: 60px;\n  margin-left: auto;\n}\n\n/* Enhanced Loading and Empty States */\n.loading-state,\n.empty-state,\n.error-state {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 60px 20px;\n  text-align: center;\n  animation: fadeIn 0.5s ease-out;\n}\n\n.loading-state {\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n  border-radius: 12px;\n  margin: 20px;\n  border: 1px solid #e9ecef;\n}\n\n.empty-state {\n  border-radius: 12px;\n  margin: 20px;\n}\n\n.error-state {\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\n  border-radius: 12px;\n  margin: 20px;\n  border: 1px solid #fed7d7;\n}\n\n/* Icons */\n.loading-icon,\n.empty-icon,\n.error-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  animation: pulse 2s infinite;\n}\n\n.loading-icon {\n  animation: pulse 1.5s infinite;\n}\n\n.empty-icon {\n  opacity: 0.7;\n}\n\n.error-icon {\n  color: #f44336;\n}\n\n/* Text Styling */\n.loading-title,\n.empty-title,\n.error-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 8px 0;\n  line-height: 1.4;\n}\n\n.loading-subtitle,\n.empty-subtitle,\n.error-subtitle {\n  font-size: 14px;\n  color: #718096;\n  margin: 0;\n  line-height: 1.5;\n  max-width: 300px;\n}\n\n.error-title {\n  color: #e53e3e;\n}\n\n.error-subtitle {\n  color: #c53030;\n}\n\n/* Legacy support for existing p tags */\n.empty-state p,\n.loading-state p,\n.error-state p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.error-state {\n  color: #f44336;\n}\n\n/* Responsive Design for Table */\n@media (max-width: 768px) {\n  .instruments-table {\n    min-width: 500px; /* Reduce minimum width for tablets */\n  }\n\n  .table-header {\n    padding: 6px 8px;\n    font-size: 11px;\n  }\n\n  .table-row td {\n    padding: 6px 8px;\n    font-size: 13px;\n  }\n\n  .cell.symbol {\n    gap: 1px;\n  }\n\n  .symbol-text {\n    font-size: 13px;\n  }\n\n  .market-text {\n    font-size: 10px;\n  }\n\n  /* Adjust column widths for tablets */\n  .instruments-table th:nth-child(1) { /* Symbol */\n    width: 40%;\n    min-width: 100px;\n  }\n\n  .instruments-table th:nth-child(2) { /* Last Price */\n    width: 25%;\n    min-width: 80px;\n  }\n\n  .instruments-table th:nth-child(3) { /* Change */\n    width: 18%;\n    min-width: 70px;\n  }\n\n  .instruments-table th:nth-child(4) { /* Change% */\n    width: 17%;\n    min-width: 80px;\n  }\n\n  .change-value {\n    font-size: 12px;\n  }\n\n  .remove-btn {\n    padding: 4px;\n  }\n}\n\n/* Responsive Design for States */\n@media (max-width: 768px) {\n  .loading-state,\n  .empty-state,\n  .error-state {\n    padding: 40px 16px;\n    margin: 16px;\n  }\n\n  .loading-icon,\n  .empty-icon,\n  .error-icon {\n    font-size: 36px;\n    margin-bottom: 12px;\n  }\n\n  .loading-title,\n  .empty-title,\n  .error-title {\n    font-size: 16px;\n  }\n\n  .loading-subtitle,\n  .empty-subtitle,\n  .error-subtitle {\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 480px) {\n  .instruments-table {\n    min-width: 400px; /* Further reduce for mobile */\n  }\n\n  .table-header {\n    padding: 4px 6px;\n    font-size: 10px;\n  }\n\n  .table-row td {\n    padding: 4px 6px;\n    font-size: 12px;\n  }\n\n  .symbol-text {\n    font-size: 12px;\n  }\n\n  .market-text {\n    font-size: 9px;\n  }\n\n  /* Mobile column widths - more compact */\n  .instruments-table th:nth-child(1) { /* Symbol */\n    width: 45%;\n    min-width: 90px;\n  }\n\n  .instruments-table th:nth-child(2) { /* Last Price */\n    width: 25%;\n    min-width: 70px;\n  }\n\n  .instruments-table th:nth-child(3) { /* Change */\n    width: 15%;\n    min-width: 60px;\n  }\n\n  .instruments-table th:nth-child(4) { /* Change% */\n    width: 15%;\n    min-width: 70px;\n  }\n\n  .remove-btn {\n    padding: 3px;\n  }\n\n  .change-value {\n    font-size: 11px;\n  }\n\n  .cell.change-percent {\n    gap: 4px;\n  }\n\n  .skeleton-symbol-text {\n    width: 60px;\n  }\n\n  .skeleton-market-text {\n    width: 45px;\n  }\n\n  .skeleton-price {\n    width: 50px;\n  }\n\n  .skeleton-change {\n    width: 40px;\n  }\n\n  .skeleton-change-percent {\n    width: 45px;\n  }\n\n  .loading-state,\n  .empty-state,\n  .error-state {\n    padding: 30px 12px;\n    margin: 12px;\n  }\n\n  .loading-icon,\n  .empty-icon,\n  .error-icon {\n    font-size: 32px;\n    margin-bottom: 10px;\n  }\n\n  .loading-title,\n  .empty-title,\n  .error-title {\n    font-size: 15px;\n  }\n\n  .loading-subtitle,\n  .empty-subtitle,\n  .error-subtitle {\n    font-size: 12px;\n  }\n}\n\n/* Extra small screens */\n@media (max-width: 360px) {\n  .instruments-table {\n    min-width: 320px; /* Minimum for very small screens */\n  }\n\n  .table-header {\n    padding: 3px 4px;\n    font-size: 9px;\n  }\n\n  .table-row td {\n    padding: 3px 4px;\n    font-size: 11px;\n  }\n\n  .symbol-text {\n    font-size: 11px;\n  }\n\n  .market-text {\n    font-size: 8px;\n  }\n\n  /* Very compact column widths */\n  .instruments-table th:nth-child(1) { /* Symbol */\n    width: 50%;\n    min-width: 80px;\n  }\n\n  .instruments-table th:nth-child(2) { /* Last Price */\n    width: 25%;\n    min-width: 60px;\n  }\n\n  .instruments-table th:nth-child(3) { /* Change */\n    width: 12.5%;\n    min-width: 50px;\n  }\n\n  .instruments-table th:nth-child(4) { /* Change% */\n    width: 12.5%;\n    min-width: 60px;\n  }\n\n  .remove-btn {\n    padding: 2px;\n  }\n\n  .change-value {\n    font-size: 10px;\n  }\n\n  .cell.change-percent {\n    gap: 2px;\n  }\n}",this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(e),this.shadowRoot.appendChild(this.container),this.container&&U($(co,{client:cu,children:$(Kn,{value:Vn,children:$(iu,{})})}),this.container)}disconnectedCallback(){this.container&&U(null,this.container)}}class uu extends HTMLElement{container=null;connectedCallback(){this.attachShadow({mode:"open"});const e=document.createElement("style");e.textContent=Gn,e.textContent+=Jn,e.textContent+=Yn,this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(e),this.shadowRoot.appendChild(this.container),this.container&&U($(co,{client:cu,children:$(Kn,{value:Vn,children:$(Co,{instrumentId:this.getAttribute("instrumentId")})})}),this.container)}disconnectedCallback(){this.container&&U(null,this.container)}}class du extends HTMLElement{container=null;connectedCallback(){this.attachShadow({mode:"open"});const e=document.createElement("style");e.textContent=Gn,e.textContent+=Jn,e.textContent+=Yn,this.container=document.createElement("div"),this.container.id="preact-root",this.shadowRoot.appendChild(e),this.shadowRoot.appendChild(this.container),"/tools/sharegraph3-realtime/watchlist-add-instrument"===location.pathname&&U($(co,{client:cu,children:$(Kn,{value:Vn,children:$(su,{})})}),this.container)}disconnectedCallback(){this.container&&U(null,this.container)}}customElements.define("euroland-watch-list",lu),customElements.define("euroland-add-instrument-button",uu),customElements.define("euroland-add-instrument-modal",du)});
//# sourceMappingURL=watchlist-widget.umd.js.map
