{
  "Urls": "https://*:5005",
  "DetailedErrors": true,
  "Serilog": {
    "Using": ["Serilog.Sinks.Console" ],
    "MinimumLevel": "Debug",
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ]
  },
  "ConnectionStrings": {
    "NewsDb": "Server=************;Database=News;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=true;Application Name=SData.API",
    "SharkDb": "Server=************;Database=shark;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=true;Application Name=SData.API",
    "SharkRealtimeDb": "Server=tcp:eurolandeurope.database.windows.net,1433;Database=shark;User ID=uShark;Password=**********;Trusted_Connection=False;TrustServerCertificate=true;MultipleActiveResultSets=True;Application Name=SData.API",
    "WebcastDb": "Server=************;Database=Webcast;User ID=uShark;Password=**********;MultipleActiveResultSets=true;Trusted_Connection=False;TrustServerCertificate=True;Application Name=SData.API",
    "TimeScaleDb": "Host=***********;Port=5432;Database=eurolandtimescale;Username=postgres;Password=**********;Trust Server Certificate=true"
  },
  "DistributedCachingConfiguration": {
    "UseRedisCache": false,
    "RedisConnectionString": "localhost:7000",
    "RedisInstanceName ": "redis_01",

    //"UseNCache": false
    //"UseSQLServerCache": false

    "CacheSizeLimit": 0, // case value = 0 we don't limit cache size
    "CompactionPercentage": 0, // the amount to compact the cache by when the maximum size is exceeded
    "ExpirationTime": 30 // 30s
  },
  "ShareGraph": {
    "Webcast": {
      "PdfUrlTemplate": "https://vimeo.com/{0}"
    }
  }
}
